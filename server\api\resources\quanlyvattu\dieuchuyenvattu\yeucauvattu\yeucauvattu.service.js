import * as ValidatorHelper from '../../../../helpers/validatorHelper';
import DIEUCHUYENVATTU from '../dieuchuyenvattu.model';
import * as YeuCauChiTietService from './yeucauvattuchitiet/yeucauvattuchitiet.service';

export async function validateTonkho(data) {
  const allChitiet = await YeuCauChiTietService.getAll({ id_yeu_cau_vattu: data._id });
  return await YeuCauChiTietService.checkTonKho(data.id_donvi_di, allChitiet);
}

const YEUCAUVATTU = DIEUCHUYENVATTU;

export async function getAll(query) {
  return YEUCAUVATTU.find(query).lean();
}

const Joi = require('joi');

const objSchema = Joi.object({
  id_donvi_di: Joi.string().required().messages(ValidatorHelper.messageDefine('Đơn vị thực hiện')),
  id_nguoi_di: Joi.string().required().messages(ValidatorHelper.messageDefine('Người thực hiện')),
  id_donvi_den: Joi.string().messages(ValidatorHelper.messageDefine('Đơn vị tiếp nhận')),
  id_nguoi_den: Joi.string().required().messages(ValidatorHelper.messageDefine('Người tiếp nhập')),
  thoi_gian_yeu_cau: Joi.date().required().messages(ValidatorHelper.messageDefine('Ngày yêu cầu')),
  thoi_gian_duyet: Joi.date().messages(ValidatorHelper.messageDefine('Ngày duyệt')),
  ly_do: Joi.string().required().messages(ValidatorHelper.messageDefine('Lý do')),
  lanh_dao_duyet: Joi.boolean().required().messages(ValidatorHelper.messageDefine('Lãnh đạo duyệt')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}
