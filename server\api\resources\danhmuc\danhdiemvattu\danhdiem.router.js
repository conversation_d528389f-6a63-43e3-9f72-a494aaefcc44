import express from 'express';
import passport from 'passport';
import * as danhdiemController from './danhdiem.controller';
import { authorizationMiddleware } from '../../rbac/middleware';
import DanhMucPermission from '../../rbac/permissions/danhmuc/DanhMucPermission';

export const danhdiemRouter = express.Router();

danhdiemRouter.use(passport.authenticate('jwt', { session: false }));
danhdiemRouter.get('*', authorizationMiddleware([DanhMucPermission.READ]));
danhdiemRouter.post('*', authorizationMiddleware([DanhMucPermission.CREATE]));
danhdiemRouter.put('*', authorizationMiddleware([DanhMucPermission.UPDATE]));
danhdiemRouter.delete('*', authorizationMiddleware([DanhMucPermission.DELETE]));

danhdiemRouter
  .route('/')
  .get(danhdiemController.getAll)
  .post(danhdiemController.create);

danhdiemRouter
  .route('/:id')
  .get(danhdiemController.findOne)
  .delete(danhdiemController.remove)
  .put(danhdiemController.update);
