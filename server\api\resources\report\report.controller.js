import queryHelper from '../../helpers/queryHelper';
import * as Service from './report.service';
import { generateDocument } from './report.service';
import * as TonKhoService from '../quanlyvattu/tonkho/tonkho.service';
import * as DieuChuyenChiTietService
  from '../quanlyvattu/dieuchuyenvattu/dieuchuyenvattuchitiet/dieuchuyenvattuchitiet.service';
import * as DieuChuyenService from '../quanlyvattu/dieuchuyenvattu/dieuchuyenvattu.service';
import * as TiepNhanService from '../quanlyvattu/tiepnhan/service';
import * as TiepNhanChiTietService from '../quanlyvattu/tiepnhan/chitiet/service';
import * as LichSuService from '../quanlyvattu/lichsu/service';
import { LOAI_DIEU_CHUYEN } from '../../constant/constant';
import { formatDate, formatToDateDetail, groupBy } from '../../common/functionCommons';


export async function baoCaoTonKhoTheoVatTu(req, res) {
  const query = queryHelper.extractQueryParam(req);
  const { criteria } = query;
  let tgBatDau, tgKetThuc, idDonvi;
  tgBatDau = criteria.tg_dau_ky;
  tgKetThuc = criteria.tg_cuoi_ky;
  idDonvi = criteria.don_vi_id;
  const tonKhoTheoVatTu = await TonKhoService.tonkhoTheoVattu(tgBatDau, tgKetThuc, idDonvi);
  const result = await Service.dataBaoCaoTonKhoTheoVatTu(tonKhoTheoVatTu, idDonvi, tgBatDau, tgKetThuc);
  const fileName = 'bao_cao_ton_kho_theo_vat_tu.docx';
  generateDocument(res, result, fileName);
}

export async function baoCaoTonKhoTheoDanhDiem(req, res) {
  const query = queryHelper.extractQueryParam(req);
  const { criteria } = query;
  let tgBatDau, tgKetThuc, idDonvi;
  tgBatDau = criteria.tg_dau_ky;
  tgKetThuc = criteria.tg_cuoi_ky;
  idDonvi = criteria.don_vi_id;
  const tonKhoTheoDanhDiem = await TonKhoService.tonkhoTheoDanhDiem(tgBatDau, tgKetThuc, idDonvi);
  let result = await Service.convertDataBaoCaoTonKhoTheoDanhDiem(tonKhoTheoDanhDiem, idDonvi, tgBatDau, tgKetThuc);
  result.ly_do_tang = groupBy(await Service.lyDoTangVatTu(tgBatDau, tgKetThuc, idDonvi), 'type');
  result.ly_do_giam = groupBy(await Service.lyDoGiamVatTu(tgBatDau, tgKetThuc, idDonvi), 'type');
  const fileName = 'bao_cao_ton_kho_danh_diem_theo_loai_vat_tu_1.docx';
  generateDocument(res, result, fileName);
}

export async function baoCaoThucLucToanDonVi(req, res) {
  const query = queryHelper.extractQueryParam(req);
  const { criteria } = query;
  let tgBatDau, tgKetThuc, idDonvi;
  tgBatDau = criteria.tg_dau_ky;
  tgKetThuc = criteria.tg_cuoi_ky;
  idDonvi = criteria.don_vi_id;
  const tonKhoTheoDanhDiem = await TonKhoService.tonkhoTheoDanhDiem(tgBatDau, tgKetThuc);
  const tonKhoDonVi = await TonKhoService.tonKhoTheoDonVi(tonKhoTheoDanhDiem, idDonvi);
  tonKhoDonVi.tg_dau_ky = formatDate(tgBatDau);
  tonKhoDonVi.tg_cuoi_ky = formatDate(tgKetThuc);
  tonKhoDonVi.ngay = formatToDateDetail(new Date()).ngay;
  tonKhoDonVi.thang = formatToDateDetail(new Date()).thang;
  tonKhoDonVi.nam = formatToDateDetail(new Date()).nam;
  const fileName = 'bao_cao_thuc_luc_toan_don_vi.docx';
  generateDocument(res, tonKhoDonVi, fileName);
}

export async function bienBanDieuChuyen(req, res) {
  const query = queryHelper.extractQueryParam(req);
  const { criteria } = query;
  const dataDieuChuyenChiTiet = await DieuChuyenChiTietService.getAll({
    id_dieu_chuyen: criteria.id,
    is_deleted: false,
  });
  const dataDieuChuyen = await DieuChuyenService.getAll({ _id: criteria.id, is_deleted: false })
  const result = await Service.convertDataDieuChuyen(dataDieuChuyen, dataDieuChuyenChiTiet);

  let fileName = undefined;
  if (criteria.type === LOAI_DIEU_CHUYEN.DIEU_CHUYEN) {
    fileName = 'bien_ban_dieu_chuyen.docx';
  }
  if (criteria.type === LOAI_DIEU_CHUYEN.DIEU_DONG) {
    fileName = 'bien_ban_dieu_dong.docx';
  }
  if (criteria.type === LOAI_DIEU_CHUYEN.BAN_GIAO) {
    fileName = 'bien_ban_ban_giao.docx';
  }

  generateDocument(res, result, fileName);
}

export async function bienBanLichSuVuKhi(req, res) {
  const { id } = req.params;
  const lichSuVattu = await LichSuService.getLichSuVattu(id);
  const result = await Service.convertDataLichSuVatTu(lichSuVattu);
  const fileName = 'bien_ban_lich_su_vat_tu.docx';
  generateDocument(res, result, fileName);
}

export async function bienBanTiepNhanVatTu(req, res) {
  const { id } = req.params;
  const dataTiepNhanChiTiet = await TiepNhanChiTietService.getAll({
    id_tiepnhan: id,
    is_deleted: false,
  }).populate({ path: 'id_tinhtrang' });
  const dataTiepNhan = await TiepNhanService.getAll({
    _id: id,
    is_deleted: false,
  });
  const result = await Service.convertDataTiepNhanVatTu(dataTiepNhan, dataTiepNhanChiTiet);
  const fileName = `tiep_nhan_vat_tu.docx`;
  generateDocument(res, result, fileName);
}

