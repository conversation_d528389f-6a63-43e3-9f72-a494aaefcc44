import * as ValidatorHelper from '../../../helpers/validatorHelper';
import TIEUHUY from './model';
import { createEventQuery, formatDate, groupBy } from '../../../common/functionCommons';
import * as TieuHuyChiTietService from './chitiet/service';

const Joi = require('joi');

const objSchema = Joi.object({
  id_nguoi: Joi.string().required().messages(ValidatorHelper.messageDefine('Người tiến hành')),
  id_donvi: Joi.string().required().messages(ValidatorHelper.messageDefine('Đơn vị')),
  trang_thai: Joi.boolean().messages(ValidatorHelper.messageDefine('Hoàn thành')),
  thoi_gian_thuc_hien: Joi.date().required().messages(ValidatorHelper.messageDefine('<PERSON><PERSON><PERSON> thanh lý')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

export async function getAll(query) {
  return TIEUHUY.find(query).lean();
}

export async function getForLyDoGiam(tgBatDau, tgKetThuc, idDonvi) {
  const queryTieuHuy = createEventQuery(tgBatDau, tgKetThuc);
  if (idDonvi) {
    queryTieuHuy.id_donvi = idDonvi;
  }
  const tieuHuy = (await TIEUHUY.find(queryTieuHuy)).map(item => item._id);
  let allTieuHuyChitiet = await TieuHuyChiTietService.getAll({ id_tieuhuy: { $in: tieuHuy } })
    .populate( { path: 'id_vattu id_tieuhuy', populate: { path: 'id_danh_diem', populate: 'ma_don_vi_tinh' } },);

  allTieuHuyChitiet.forEach(item => {
    item.id_danh_diem = item.id_vattu.id_danh_diem._id;
  });
  const groupByDanhDiem = groupBy(allTieuHuyChitiet, 'id_danh_diem');

  const mapDanhDiem = {};
  allTieuHuyChitiet.forEach(item => {
    if (!mapDanhDiem[item.id_danh_diem]) {
      mapDanhDiem[item.id_danh_diem] = {
        ...{
          so_luong: item.soluong,
          loai: item.id_vattu.id_danh_diem.ten_danh_diem,
          don_vi_tinh: item.id_vattu.id_danh_diem.ma_don_vi_tinh.ten_don_vi_tinh.toLowerCase(),
          ngay_tieu_huy: item.id_tieuhuy.thoi_gian_hoan_thanh ? formatDate(item.id_tieuhuy.thoi_gian_hoan_thanh) : '',
        },
      };
    } else {
      mapDanhDiem[item.id_danh_diem].so_luong += item.soluong;
    }
  });
  let lyDoGiam = [];
  Object.keys(groupByDanhDiem).forEach(danhdiem => {
    lyDoGiam.push({ ...mapDanhDiem[danhdiem], ...{ stt: '-', type: 'tieu_huy' } });
  });
  return lyDoGiam;
}
