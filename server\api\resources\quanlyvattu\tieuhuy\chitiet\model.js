import mongoose, { Schema } from 'mongoose';
import { KHOVATTU, TIEUHUY_CHITIET, VATTU, TIEUHUY, TINHTRANG } from '../../../../constant/dbCollections';
import mongoosePaginate from 'mongoose-paginate-v2';

const schema = new Schema({
  id_tieuhuy: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: TIEUHUY,
  },
  id_vattu: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: VATTU,
  },
  id_kho: {
    type: mongoose.Schema.Types.ObjectId,
    required: false,
    ref: KHOVATTU,
  },
  id_tinhtrang: {
    type: mongoose.Schema.Types.ObjectId,
    // required: false,
    ref: TINHTRANG,
  },
  ghichu: { type: String },
  soluong: { type: Number, required: true },
  isActive: { type: Boolean, default: true },
  is_deleted: { type: <PERSON>olean, default: false, select: false },
}, {
  versionKey: false,
});
schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(TIEUHUY_CHITIET, schema);
