import mongoose, {Schema} from 'mongoose';
import { ROLE } from '../../constant/dbCollections';

const RoleSchema = new Schema({
  code: {type: String, required: true, unique: true},
  name: {type: String, required: true},
  description: {type: String},
  permissions: [{type: String}],
  isActive: { type: Boolean, default: true },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  versionKey: false,
});
export {RoleSchema as DocumentSchema};
export default mongoose.model(ROLE, RoleSchema);
