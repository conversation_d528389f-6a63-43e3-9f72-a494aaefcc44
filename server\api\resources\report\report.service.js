import carbone from 'carbone';
import { getDirPath, getFilePath } from '../../utils/fileUtils';
import * as responseAction from '../../helpers/responseHelper';
import * as DonViService from '../tochucquanly/donVi.service';
import * as TinhTrangService from '../danhmuc/tinhtrang/tinhtrang.service';
import * as LoaiVatTuService from '../danhmuc/loaivattu/loaivattu.service';
import * as DieuChuyenService from '../quanlyvattu/dieuchuyenvattu/dieuchuyenvattu.service';
import * as TiepNhanService from '../quanlyvattu/tiepnhan/service';
import * as ThanhLyService from '../quanlyvattu/thanhlyvattu/thanhlyvattu.service';
import * as TieuHuyService from '../quanlyvattu/tieuhuy/service';
import { formatDate, formatToDateDetail, groupBy } from '../../common/functionCommons';
import { romanize } from '../../common/romanize';
import {
  BAN_GIAO, DIEU_CHUYEN, DIEU_DONG,
  GIAOVATTU_CANHAN, THANHLY, TIEP_NHAN,
  TIEUHUY, TRA_VATTU, VATTU_DAUKY,
} from '../../constant/dbCollections';

export function generateDocument(res, data, fileName) {
  let opt = {
    renderPrefix: 'bao_cao',
    reportName: 'Báo cáo',
    timezone: 'Asia/Saigon',
  };
  const templateFilePath = getFilePath(fileName, getDirPath('templates', './server'));
  carbone.render(templateFilePath, data, opt, async function(err, resultFilePath) {
    try {
      res.download(resultFilePath);
    } catch (err) {
      responseAction.error(res, { message: 'Không thể tải được tập tin!' }, 400);
    }
  });
}

export async function groupByLoaiVatTu(dataTonKho, loaivatTuIds) {
  const groupByLoaiVatTuId = groupBy(dataTonKho, 'loai_vat_tu_id');

  const loaiVatTu = await LoaiVatTuService.getAll({ _id: { $in: loaivatTuIds }, is_deleted: false });
  loaiVatTu.forEach((loai, index) => {
    loai.thu_tu = romanize(index + 1);
    loai.tonkho = groupByLoaiVatTuId[loai._id];
  });
  loaiVatTu.forEach(loai => {
    loai.tonkho.forEach((tonKho, index) => {
      tonKho.thu_tu = index + 1;
    });
  });
  return loaiVatTu;
}

export async function dataBaoCaoTonKhoTheoVatTu(data, idDonvi, tgBatDau, tgKetThuc) {
  let dataResponse = {};
  const donVi = await DonViService.getAll({ _id: idDonvi });
  dataResponse.don_vi = donVi[0]?.ten_don_vi;
  dataResponse.tg_dau_ky = formatDate(tgBatDau);
  dataResponse.tg_cuoi_ky = formatDate(tgKetThuc);
  const allTinhTrang = await TinhTrangService.getAll({ is_deleted: false });
  allTinhTrang.forEach(item => {
    item.cap = item.ten_tinh_trang;
  });

  data.forEach(item => {
    item.phancap_tt = [];
    let mapTinhTrang = {};
    item.tinhtrang.forEach(docs => {
      mapTinhTrang[docs.id_tinh_trang._id] = {
        id: docs.id_tinh_trang._id,
        soluong: docs.tonkho_cuoiky,
        soluong_trongkho: docs.soluong_trongkho_thucte,
      };
    });
    allTinhTrang.forEach(docs => {
      if (!mapTinhTrang[docs._id]) {
        item.phancap_tt.push({
          id: docs._id,
          soluong: 0,
          soluong_trongkho: 0,
        });
      } else {
        item.phancap_tt.push({
          id: mapTinhTrang[docs._id].id,
          soluong: mapTinhTrang[docs._id].soluong,
          soluong_trongkho: mapTinhTrang[docs._id].soluong_trongkho,
        });
      }
    });
  });
  dataResponse.all_tinh_trang = allTinhTrang;
  let loaivatTuIds = [];

  function convertDataToRow(tonKho, index) {
    loaivatTuIds = [...loaivatTuIds, tonKho?.id_vattu?.id_danh_diem?.loai_vat_tu_id._id];
    return {
      stt: index + 1,
      don_vi_tinh: tonKho.id_vattu.id_danh_diem?.ma_don_vi_tinh?.ten_don_vi_tinh,
      phancap_tt: tonKho.phancap_tt,
      loai_vat_tu_id: tonKho.id_vattu?.id_danh_diem?.loai_vat_tu_id._id,
      loai_vat_tu: tonKho.id_vattu.id_danh_diem.loai_vat_tu_id,
      id_danh_diem: tonKho.id_vattu?.id_danh_diem,
      soluong_tang: tonKho.soluong_tang,
      soluong_giam: tonKho.soluong_giam,
      tonkho_cuoiky: tonKho.tonkho_cuoiky,
      tonkho_dauky: tonKho.tonkho_dauky,
      so_giay_phep: tonKho.id_vattu?.so_giay_phep,
      serial: tonKho.id_vattu?.serial,
    };
  }

  const allTonKho = data.map(convertDataToRow);
  const loaiVatTu = await groupByLoaiVatTu(allTonKho, loaivatTuIds);
  dataResponse.ton_kho = data.map(convertDataToRow);
  dataResponse.loai_vat_tu = loaiVatTu;
  return dataResponse;
}

export async function convertDataBaoCaoTonKhoTheoDanhDiem(tonKhoTheoDanhDiem, idDonvi, tgBatDau, tgKetThuc) {
  let dataResponse = {};
  const donVi = await DonViService.getAll({ _id: idDonvi });
  dataResponse.don_vi = donVi[0]?.ten_don_vi;
  dataResponse.tg_dau_ky = tgBatDau ? formatDate(tgBatDau) : '';
  dataResponse.tg_cuoi_ky = tgKetThuc ? formatDate(tgKetThuc) : '';
  const allTinhTrang = await TinhTrangService.getAll({ is_deleted: false });
  allTinhTrang.forEach(item => {
    item.cap = item.ten_tinh_trang;
  });
  tonKhoTheoDanhDiem.forEach(data => {
    let mapTinhTrang = {};
    data.phancap_tt = [];
    data.tinhtrang.forEach(item => {
      if (!mapTinhTrang[item.id_tinh_trang]) {
        mapTinhTrang[item.id_tinh_trang] = {
          id: item.id_tinh_trang,
          soluong: item.tonkho_cuoiky,
          soluong_trongkho: item.soluong_trongkho_thucte,
        };
      } else {
        mapTinhTrang[item.id_tinh_trang].soluong += item.tonkho_cuoiky;
        mapTinhTrang[item.id_tinh_trang].soluong_trongkho += item.soluong_trongkho_thucte;
      }
    });
    allTinhTrang.forEach(tinhTrang => {
      if (mapTinhTrang[tinhTrang._id]) {
        data.phancap_tt.push({
          id: tinhTrang._id,
          soluong: mapTinhTrang[tinhTrang._id].soluong,
          soluong_trongkho: mapTinhTrang[tinhTrang._id].soluong_trongkho,
        });
      } else {
        data.phancap_tt.push({
          id: tinhTrang._id,
          soluong: 0,
          soluong_trongkho: 0,
        });
      }
    });
  });

  dataResponse.all_tinh_trang = allTinhTrang;
  let loaivatTuIds = [];

  function convertDataToRow(tonKho) {
    loaivatTuIds = [...loaivatTuIds, tonKho?.id_danh_diem?.loai_vat_tu_id];
    return {
      ten_danh_diem: tonKho.id_danh_diem.ten_danh_diem,
      loai_vat_tu_id: tonKho.id_danh_diem?.loai_vat_tu_id,
      don_vi_tinh: tonKho.id_danh_diem?.ma_don_vi_tinh?.ten_don_vi_tinh,
      tonkho_dauky: tonKho.tonkho_dauky,
      tonkho_cuoiky: tonKho.tonkho_cuoiky,
      soluong_giam: tonKho.soluong_giam,
      soluong_tang: tonKho.soluong_tang,
      phancap_tt: tonKho.phancap_tt,
    };
  }

  const allTonKhoDanhDiem = tonKhoTheoDanhDiem.map(convertDataToRow);
  const loaiVatTu = await groupByLoaiVatTu(allTonKhoDanhDiem, loaivatTuIds);

  dataResponse.ton_kho = tonKhoTheoDanhDiem.map(convertDataToRow);
  dataResponse.loai_vat_tu = loaiVatTu;
  return dataResponse;
}

export async function convertDataDieuChuyen(dataDieuChuyen, dataDieuChuyenChiTiet) {
  let dataResponse = {};
  dataResponse = Object.assign(dataResponse, {}, dataDieuChuyen[0]);
  dataResponse.chitiet = dataDieuChuyenChiTiet.map((item, index) => {
    item.stt = index + 1;
    return item;
  });
  dataResponse.ngay = formatToDateDetail(new Date()).ngay;
  dataResponse.thang = formatToDateDetail(new Date()).thang;
  dataResponse.nam = formatToDateDetail(new Date()).nam;
  // console.log('dataResponse', dataResponse.chitiet[0]);
  return dataResponse;
}

export async function convertDataLichSuVatTu(lichSuVattu) {
  let dataResponse = {};
  dataResponse.ngay = formatToDateDetail(new Date()).ngay;
  dataResponse.thang = formatToDateDetail(new Date()).thang;
  dataResponse.nam = formatToDateDetail(new Date()).nam;

  function convertLichSuToDB(item) {
    switch (item.event) {
      case THANHLY:
        item.event = 'Thanh lý vật tư';
        item.thoi_gian_thuc_hien = item.info.id_thanhly.thoi_gian_thuc_hien;
        item.thoi_gian_hoan_thanh = item.info.id_thanhly.thoi_gian_hoan_thanh;
        item.id_donvi = { donvi_1: item.info.id_thanhly.id_donvi.ten_don_vi };
        break;
      case TIEP_NHAN:
        item.event = 'Tiếp nhận vật tư';
        item.thoi_gian_thuc_hien = item.info.id_tiepnhan.thoi_gian_thuc_hien;
        item.thoi_gian_hoan_thanh = item.info.id_tiepnhan.thoi_gian_hoan_thanh;
        item.id_donvi = { donvi_1: item.info.id_tiepnhan.id_donvi.ten_don_vi };
        break;
      case TIEUHUY:
        item.event = 'Tiêu hủy vật tư';
        item.thoi_gian_thuc_hien = item.info.id_tieuhuy.thoi_gian_thuc_hien;
        item.thoi_gian_hoan_thanh = item.info.id_tieuhuy.thoi_gian_hoan_thanh;
        item.id_donvi = { donvi_1: item.info.id_tieuhuy.id_donvi.ten_don_vi };
        break;
      case VATTU_DAUKY:
        item.event = 'Vật tư đầu kỳ';
        item.thoi_gian_thuc_hien = item.info.created_at;
        item.thoi_gian_hoan_thanh = item.info.created_at;
        item.id_donvi = { donvi_1: item.info.id_don_vi.ten_don_vi };
        break;
      case GIAOVATTU_CANHAN:
        item.event = 'Bàn giao vật tư cá nhân';
        item.thoi_gian_thuc_hien = item.info.id_giaonhan.thoi_gian_ban_giao_ca_nhan;
        item.thoi_gian_hoan_thanh = item.info.id_giaonhan.thoi_gian_hoan_thanh;
        item.id_donvi = { donvi_1: item.info.id_giaonhan.id_donvi.ten_don_vi };
        break;
      case TRA_VATTU:
        item.event = 'Hoàn trả vật tư';
        item.thoi_gian_thuc_hien = item.info.id_travattu.thoi_gian_tra_vat_tu;
        item.thoi_gian_hoan_thanh = item.info.id_travattu.thoi_gian_hoan_thanh;
        item.id_donvi = { donvi_1: item.info.id_travattu.id_donvi.ten_don_vi };
        break;
      case DIEU_CHUYEN:
        item.event = 'Điều chuyển vật tư';
        item.thoi_gian_thuc_hien = item.info.id_dieu_chuyen.thoi_gian_thuc_hien;
        item.thoi_gian_hoan_thanh = item.info.id_dieu_chuyen.thoi_gian_hoan_thanh;
        item.id_donvi = {
          donvi_1: item.info.id_dieu_chuyen.id_donvi_di.ten_don_vi,
          donvi_2: item.info.id_dieu_chuyen.id_donvi_den.ten_don_vi,
        };
        break;
      case DIEU_DONG:
        item.event = 'Điều động vật tư';
        item.thoi_gian_thuc_hien = item.info.id_dieu_chuyen.thoi_gian_thuc_hien;
        item.thoi_gian_hoan_thanh = item.info.id_dieu_chuyen.thoi_gian_hoan_thanh;
        item.id_donvi = {
          donvi_1: item.info.id_dieu_chuyen.id_donvi_di.ten_don_vi,
          donvi_2: item.info.id_dieu_chuyen.id_donvi_den.ten_don_vi,
        };
        break;
      case BAN_GIAO:
        item.event = 'Bàn giao vật tư';
        item.thoi_gian_thuc_hien = item.info.id_dieu_chuyen.thoi_gian_thuc_hien;
        item.thoi_gian_hoan_thanh = item.info.id_dieu_chuyen.thoi_gian_hoan_thanh;
        item.id_donvi = {
          donvi_1: item.info.id_dieu_chuyen.id_donvi_di.ten_don_vi,
          donvi_2: item.info.id_dieu_chuyen.id_donvi_den.ten_don_vi,
        };
        break;
    }
    return {
      event: item.event,
      id_vattu: item.info.id_vattu,
      thoi_gian_thuc_hien: item.thoi_gian_thuc_hien,
      thoi_gian_hoan_thanh: item.thoi_gian_hoan_thanh,
      id_donvi: item.id_donvi,
    };
  }

  const dataConvert = lichSuVattu.map(convertLichSuToDB);
  dataConvert.forEach((item, index) => item.stt = index + 1);
  dataResponse.lich_su = dataConvert;
  return dataResponse;
}

export async function convertDataTiepNhanVatTu(dataTiepNhan, dataTiepNhanChiTiet) {
  let dataResponse = {};
  dataTiepNhanChiTiet.forEach((chitiet, index) => {
    chitiet.stt = index + 1;
    return chitiet;
  });
  dataResponse = Object.assign(dataResponse, {}, dataTiepNhan[0]);
  dataResponse.ngay = formatToDateDetail(new Date()).ngay;
  dataResponse.thang = formatToDateDetail(new Date()).thang;
  dataResponse.nam = formatToDateDetail(new Date()).nam;
  dataResponse.chi_tiet = dataTiepNhanChiTiet;
  return dataResponse;
}


export async function lyDoTangVatTu(tgBatDau, tgKetThuc, idDonvi) {
  let lyDoTang = [];
  lyDoTang = [...lyDoTang, ...await DieuChuyenService.getForLyDoTang(tgBatDau, tgKetThuc, idDonvi)];
  lyDoTang = [...lyDoTang, ...await TiepNhanService.getForLyDoTang(tgBatDau, tgKetThuc, idDonvi)];
  return lyDoTang;
}

export async function lyDoGiamVatTu(tgBatDau, tgKetThuc, idDonvi) {
  let lyDoGiam = [];
  lyDoGiam = [...lyDoGiam, ...await DieuChuyenService.getForLyDoGiam(tgBatDau, tgKetThuc, idDonvi)];
  lyDoGiam = [...lyDoGiam, ...await ThanhLyService.getForLyDoGiam(tgBatDau, tgKetThuc, idDonvi)];
  lyDoGiam = [...lyDoGiam, ...await TieuHuyService.getForLyDoGiam(tgBatDau, tgKetThuc, idDonvi)];
  return lyDoGiam;
}
