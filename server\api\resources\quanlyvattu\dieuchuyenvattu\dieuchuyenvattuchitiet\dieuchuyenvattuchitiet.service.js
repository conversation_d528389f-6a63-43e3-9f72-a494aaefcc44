import * as ValidatorHelper from '../../../../helpers/validatorHelper';
import DIEUCHUYENVATTU_CHITIET from './dieuchuyenvattuchitiet.model';
import { getKhongThoaManTonKhoDanhdiem, getKhongThoaManTonKhoVattu } from '../../tonkho/tonkho.service';
import { createError } from '../../../../helpers/errorHelper';

const Joi = require('joi');

export async function create(data) {
  const { error, value } = validate(data);
  if (error) throw error;
  return DIEUCHUYENVATTU_CHITIET.create(value);
}

export async function updateAll(chitietUpdate) {
  for (const row of chitietUpdate) {
    const { error, value } = validate(row);
    if (error) throw error;
    await DIEUCHUYENVATTU_CHITIET.findByIdAndUpdate(value._id, value);
  }
}

export function getAll(query) {
  return DIEUCHUYENVATTU_CHITIET.find(query)
    .populate({
      path: 'id_vattu',
      populate: { path: 'id_danh_diem', populate: 'ma_don_vi_tinh' },
    })
    .populate({ path: 'id_tinhtrang' })
    .lean();
}

export function getAllWithInfo(query) {
  return DIEUCHUYENVATTU_CHITIET.find(query)
    .populate('id_tinhtrang')
    .populate('id_vattu')
    .populate({ path: 'id_dieu_chuyen', populate: { path: 'id_donvi_di id_donvi_den', select: 'ten_don_vi' } })
    .lean();
}

export function getForTonKho(query) {
  return DIEUCHUYENVATTU_CHITIET.find(query)
    .populate('id_dieu_chuyen')
    .lean();
}

export async function removeAll(query) {
  return DIEUCHUYENVATTU_CHITIET.updateMany(query, { is_deleted: true });
}

export async function checkTonKho(id_don_vi, chitietUpdate) {
  const khongThoaMan = await getKhongThoaManTonKhoVattu(id_don_vi, chitietUpdate);
  if (khongThoaMan && khongThoaMan.length > 0) {
    throw createError(400, 'Không thoả mãn tồn kho, vui lòng kiểm tra lại');
  }
}

const objSchema = Joi.object({
  id_dieu_chuyen: Joi.string().required().messages(ValidatorHelper.messageDefine('id Điều chuyển')),
  id_vattu: Joi.string().required().messages(ValidatorHelper.messageDefine('Vật tư')),
  id_kho: Joi.string().messages(ValidatorHelper.messageDefine('Kho')),
  id_tinhtrang: Joi.string().required().messages(ValidatorHelper.messageDefine('Tình trạng')),
  ghichu: Joi.string().required().messages(ValidatorHelper.messageDefine('Ghi chú')),
  soluong: Joi.number().required().messages(ValidatorHelper.messageDefine('Số lượng')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  if (Array.isArray(data)) {
    let validateError = null;
    data.find(itemData => {
      const { value, error } = schema.validate(itemData, { allowUnknown: true, abortEarly: true });
      if (error) validateError = error;
      return error;
    });
    if (validateError && validateError.details) {
      return { validateError };
    }
    return { value: data };
  } else {
    const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
    if (error && error.details) {
      return { error };
    }
    return { value };
  }
}
