import express from 'express';
import passport from 'passport';
import * as yeuCauVattuController from './controller';
import { authorizationMiddleware } from '../../../rbac/middleware';
import QuanLyVatTuPermisson from '../../../rbac/permissions/quanlyvattu/QuanLyVatTuPermisson';

export const router = express.Router();

router.use(passport.authenticate('jwt', { session: false }));

router.post('*', authorizationMiddleware([QuanLyVatTuPermisson.CREATE]));
router.get('*', authorizationMiddleware([QuanLyVatTuPermisson.READ]));
router.put('/:id', authorizationMiddleware([QuanLyVatTuPermisson.UPDATE]));
router.put('/duyet/:id', authorizationMiddleware([QuanLyVatTuPermisson.CONFIRM]));
router.delete('*', authorizationMiddleware([QuanLyVatTuPermisson.DELETE]));
router
  .route('/')
  .get(yeuCauVattuController.getAll)
  .post(yeuCauVattuController.create);
router
  .route('/duyet/:id')
  .put(yeuCauVattuController.update)

router
  .route('/:id')
  .get(yeuCauVattuController.findOne)
  .delete(yeuCauVattuController.remove)
  .put(yeuCauVattuController.update);
