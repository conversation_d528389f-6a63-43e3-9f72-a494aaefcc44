{"name": "bi-dashboard", "version": "1.0.0", "description": "BI Dashboard", "engines": {"npm": ">=5", "node": ">=8.15.1"}, "author": "", "license": "UNLICENSED", "scripts": {"prebuild": "npm run build:clean", "build": "cross-env NODE_ENV=production webpack --config webpack/webpack.prod.babel.js --color -p --progress --hide-modules --display-optimization-bailout", "build:clean": "rimraf ./build", "start:server": "babel-node server", "start": "cross-env NODE_ENV=development babel-watch server", "start:tunnel": "cross-env NODE_ENV=development ENABLE_TUNNEL=true babel-watch server", "prod": "npm run build && npm run buildServer && npm run start:prod", "start:prod": "cross-env NODE_ENV=production node build/server", "presetup": "npm i chalk shelljs", "coveralls": "cat ./coverage/lcov.info | coveralls", "prettify": "prettier --write", "buildServer": "rimraf ./build/server && babel server -s -d build/server", "pkg": "pkg --target=node12-win ./build/server/index.js"}, "browserslist": ["last 2 versions", "> 1%", "IE 10"], "lint-staged": {"*.js": ["npm run lint:eslint:fix", "git add --force"], "*.json": ["prettier --write", "git add --force"]}, "resolutions": {"babel-core": "7.0.0-bridge.0"}, "dependencies": {"@ant-design/icons": "^4.3.0", "@babel/polyfill": "7.4.3", "ahooks": "^2.9.1", "antd": "^4.8.5", "axios": "^0.21.0", "bcryptjs": "2.4.3", "carbone": "^3.2.3", "chalk": "2.4.2", "classnames": "^2.2.6", "compression": "1.7.4", "connect-multiparty": "^2.2.0", "connected-react-router": "6.4.0", "convert-excel-to-json": "^1.7.0", "cross-env": "5.2.0", "docx-pdf": "0.0.1", "express": "^4.17.1", "font-awesome": "^4.7.0", "fontfaceobserver": "2.1.0", "history": "4.9.0", "hoist-non-react-statics": "3.3.0", "ibm-cos-sdk": "^1.9.0", "immer": "^8.0.0", "intl": "1.2.5", "invariant": "2.2.4", "ip": "1.1.5", "jsonwebtoken": "8.2.2", "jwt-decode": "^2.2.0", "less": "^2.7.2", "libreoffice-convert": "^1.3.3", "lodash": "^4.17.20", "minimist": "1.2.0", "moment": "^2.29.1", "mongoose": "^5.11.8", "mongoose-paginate-v2": "1.3.9", "mongoose-unique-validator": "2.0.1", "node-xlsx": "^0.16.1", "nodemailer": "^6.4.17", "passport": "0.4.0", "passport-jwt": "4.0.0", "pkg": "^5.6.0", "prop-types": "15.7.2", "query-to-mongo": "^0.10.1", "request": "^2.88.2", "reselect": "4.0.0", "tailwindcss": "^2.0.1", "use-async-memo": "^1.2.2", "use-query-params": "^1.1.8", "xlsx": "^0.17.3"}, "devDependencies": {"@babel/cli": "7.4.3", "@babel/core": "7.4.3", "@babel/node": "7.0.0", "@babel/plugin-proposal-class-properties": "7.4.0", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.10.4", "@babel/plugin-proposal-optional-chaining": "^7.11.0", "@babel/plugin-syntax-dynamic-import": "7.2.0", "@babel/plugin-transform-modules-commonjs": "7.4.3", "@babel/plugin-transform-react-constant-elements": "7.2.0", "@babel/plugin-transform-react-inline-elements": "7.2.0", "@babel/preset-env": "7.4.3", "@babel/preset-react": "7.0.0", "@babel/register": "7.4.0", "add-asset-html-webpack-plugin": "3.1.3", "autoprefixer": "^10.0.2", "awesome-unoconv": "^1.0.1", "babel-core": "7.0.0-bridge.0", "babel-eslint": "10.0.1", "babel-loader": "8.0.5", "babel-plugin-dynamic-import-node": "2.2.0", "babel-plugin-import": "^1.13.0", "babel-plugin-lodash": "3.3.4", "babel-plugin-react-intl": "3.0.1", "babel-plugin-styled-components": "1.10.0", "babel-plugin-transform-react-remove-prop-types": "0.4.24", "babel-watch": "^7.0.0", "circular-dependency-plugin": "5.0.2", "compare-versions": "3.4.0", "compression-webpack-plugin": "2.0.0", "copy-webpack-plugin": "5.1.1", "coveralls": "3.0.3", "css-loader": "2.1.1", "eslint": "5.16.0", "eslint-config-airbnb": "17.1.0", "eslint-config-airbnb-base": "13.1.0", "eslint-config-prettier": "4.1.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-import-resolver-webpack": "0.11.1", "eslint-plugin-import": "2.17.2", "eslint-plugin-jsx-a11y": "6.2.1", "eslint-plugin-prettier": "3.0.1", "eslint-plugin-react": "7.12.4", "eslint-plugin-react-hooks": "1.6.0", "eslint-plugin-redux-saga": "1.0.0", "file-loader": "3.0.1", "html-loader": "0.5.5", "http-proxy-middleware": "^1.0.4", "image-webpack-loader": "4.6.0", "imports-loader": "0.8.0", "jest-cli": "24.7.1", "jest-dom": "3.1.3", "jest-styled-components": "6.3.1", "joi": "^17.4.0", "less-loader": "^6.1.1", "lint-staged": "8.1.5", "moment-timezone": "^0.5.33", "ngrok": "3.1.1", "node-plop": "0.18.0", "node-sass": "^4.14.1", "null-loader": "0.1.1", "offline-plugin": "5.0.6", "plop": "2.3.0", "postcss": "^8.1.10", "postcss-loader": "^4.1.0", "prettier": "1.17.0", "rimraf": "2.6.3", "sass-loader": "^8.0.2", "shelljs": "0.8.3", "style-loader": "0.23.1", "stylelint": "10.0.1", "stylelint-config-recommended": "2.2.0", "stylelint-config-styled-components": "0.1.1", "stylelint-processor-styled-components": "1.6.0", "svg-url-loader": "2.3.2", "terser-webpack-plugin": "1.2.3", "unoconv": "^0.1.2", "url-loader": "1.1.2", "whatwg-fetch": "3.0.0", "write-file-webpack-plugin": "^4.5.1"}}