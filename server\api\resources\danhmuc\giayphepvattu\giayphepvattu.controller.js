import * as responseAction from '../../../utils/responseAction';
import queryHelper from '../../../helpers/queryHelper';
import * as Service from './giayphepvattu.service';
import Model from './giayphepvattu.model';
import * as fileUtils from '../../../utils/fileUtils';

export async function findOne(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findById(id);
    if (!data) {
      return responseAction.error(res, 404, '');
    }
    return responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function remove(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findOneAndUpdate({ _id: id }, { is_deleted: true }, { new: true });
    if (!data) {
      return responseAction.error(res, 404, '');
    }
    return responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function update(req, res) {
  try {
    let { error, value } = Service.validate(req.body);
    if (error) return responseAction.error(res, error, 400);
    const isUnique = await Model.findOne({
      so_giay_phep: value.so_giay_phep,
      is_deleted: false,
      _id: { $ne: value._id },
    }, { _id: 1 });
    if (isUnique) {
      return responseAction.error(res, { message: 'Số giấy phép đã tồn tại, vui lòng kiểm tra và thử lại' }, 400);
    }
    let filePath = req.files.file.path;
    const file_id = fileUtils.createUniqueFileName(filePath);
    await fileUtils.createByName(filePath, file_id);
    value.file_id = file_id;
    value.file_name = req.files.file.name;
    const data = await Model.findOneAndUpdate({ _id: value._id }, value, { new: true });
    if (!data) {
      return responseAction.error(res, null, 404);
    }
    return responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function create(req, res) {

  try {
    let { error, value } = Service.validate(req.body);
    if (error) return responseAction.error(res, error, 400);
    const isUnique = await Model.findOne({
      so_giay_phep: value.so_giay_phep,
      is_deleted: false,
      _id: { $ne: value._id },
    }, { _id: 1 });
    if (isUnique) {
      return responseAction.error(res, { message: 'Số giấy phép đã tồn tại, vui lòng kiểm tra và thử lại' }, 400);
    }
    let filePath = req.files.file.path;
    const file_id = fileUtils.createUniqueFileName(filePath);
    await fileUtils.createByName(filePath, file_id);
    value.file_id = file_id;
    value.file_name = req.files.file.name;
    const data = await Model.create(value);
    return responseAction.success(res, data);
  } catch (err) {
    console.error(err);
    return responseAction.error(res, err, 500);
  }
}

export async function getAll(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria, options } = query;
    const data = await Model.paginate(criteria, options);
    return responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}
