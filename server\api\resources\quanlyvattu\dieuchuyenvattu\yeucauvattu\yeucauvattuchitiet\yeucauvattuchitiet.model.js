import mongoose, { Schema } from 'mongoose';
import { D<PERSON><PERSON>IEMVATTU, DIEUCHUYENVATTU, TINHTRANG, YEUCAUVATTU_CHITIET } from '../../../../../constant/dbCollections';
import mongoosePaginate from 'mongoose-paginate-v2';

const schema = new Schema({
  id_danhdiem: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: DANHDIEMVATTU,
  },
  id_yeu_cau_vattu: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: DIEUCHUYENVATTU,
  },
  soluong: { type: Number, required: true },
  isActive: { type: Boolean, default: true },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  versionKey: false,
});
schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(YEUCAUVATTU_CHITIET, schema);
