import * as ValidatorHelper from '../../../helpers/validatorHelper';
import TINHTRANG from './tinhtrang.model';

const Joi = require('joi');

const objSchema = Joi.object({
  ten_tinh_trang: Joi.string().required().messages(ValidatorHelper.messageDefine('Tên tình trạng')),
  ma_tinh_trang: Joi.string().required().messages(ValidatorHelper.messageDefine('Mã tình trạng')),
  mo_ta_tinh_trang: Joi.string().messages(ValidatorHelper.messageDefine('<PERSON><PERSON> tả')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

export function getAll(query) {
  return TINHTRANG.find(query).lean();
}
