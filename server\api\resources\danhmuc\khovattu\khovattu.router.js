import express from 'express';
import passport from 'passport';
import * as khovattuController from './khovattu.controller';

export const khovattuRouter = express.Router();
khovattuRouter
  .route('/')
  .get(passport.authenticate('jwt', { session: false }), khovattuController.getAll)
  .post(passport.authenticate('jwt', { session: false }), khovattuController.create)

  khovattuRouter
  .route('/:id')
  .get(passport.authenticate('jwt', { session: false }), khovattuController.findOne)
  .delete(passport.authenticate('jwt', { session: false }), khovattuController.remove)
  .put(passport.authenticate('jwt', { session: false }), khovattuController.update);
