import * as ValidatorHelper from '../../../../../helpers/validatorHelper';
import YEUCAUVATTU_CHITIET from './yeucauvattuchitiet.model';
import { getKhongThoaManTonKhoDanhdiem } from '../../../tonkho/tonkho.service';
import { createError } from '../../../../../helpers/errorHelper';

export async function checkTonKho(id_don_vi, chitietUpdate) {
  const khongThoaMan = await getKhongThoaManTonKhoDanhdiem(id_don_vi, chitietUpdate);
  if (khongThoaMan && khongThoaMan.length > 0) {
    throw createError(400, 'Không thoả mãn tồn kho, vui lòng kiểm tra lại');
  }
}

export async function removeAll(query) {
  return YEUCAUVATTU_CHITIET.updateMany(query, { is_deleted: true });
}

export async function updateAll(chitietUpdate) {

  for (const row of chitietUpdate) {
    const { error, value } = validate(row);
    if (error) throw error;
    await YEUCAUVATTU_CHITIET.findByIdAndUpdate(value._id, value);
  }
}

export function getForTonKho(query) {
  return YEUCAUVATTU_CHITIET.find(query)
    .populate('id_yeu_cau_vattu')
    .lean();
}

export function getAll(query) {
  return YEUCAUVATTU_CHITIET.find(query).lean();
}

export async function create(data) {
  const { error, value } = validate(data);
  if (error) throw error;
  return YEUCAUVATTU_CHITIET.create(value);
}

const Joi = require('joi');

const objSchema = Joi.object({
  id_danhdiem: Joi.string().required().messages(ValidatorHelper.messageDefine('Danh điểm vật tư')),
  id_yeu_cau_vattu: Joi.string().required().messages(ValidatorHelper.messageDefine('Yêu cầu vật tư')),
  soluong: Joi.number().required().messages(ValidatorHelper.messageDefine('Số lượng')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  if (Array.isArray(data)) {
    let validateError = null;
    data.find(itemData => {
      const { value, error } = schema.validate(itemData, { allowUnknown: true, abortEarly: true });
      if (error) validateError = error;
      return error;
    });
    if (validateError && validateError.details) {
      return { validateError };
    }
    return { value: data };
  } else {
    const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
    if (error && error.details) {
      return { error };
    }
    return { value };
  }
}
