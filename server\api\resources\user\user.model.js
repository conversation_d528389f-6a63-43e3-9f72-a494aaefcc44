import mongoose from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';
import { DONVI, ROLE, TOCHUC_QUANLY } from '../../constant/dbCollections';

const { Schema } = mongoose;
const userSchema = new Schema({
    full_name: { type: String, required: true },
    email: { type: String },
    username: { type: String, required: true, unique: true },
    password: { type: String, required: true },
    gender: { type: String },
    phone: { type: String },
    is_deleted: { type: Boolean, default: false, select: false },
    active: { type: Boolean, default: true },
    id_don_vi: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
      ref: TOCHUC_QUANLY,
    },
    // id_don_vi: {
    //   type: mongoose.Schema.Types.ObjectId,
    //   required: true,
    //   ref: TOCHUC_QUANLY,
    // },
    role_id: [{ type: Schema.Types.ObjectId, ref: ROLE }],
    is_system_admin: { type: Boolean, default: false },
  },
  {
    timestamps: {
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    },
  });

userSchema.plugin(mongoosePaginate);

export default mongoose.model('User', userSchema);
