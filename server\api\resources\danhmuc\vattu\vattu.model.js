import mongoose, { Schema } from 'mongoose';
import { VATTU, DAN<PERSON><PERSON><PERSON>VATTU, DONVITINH, GIAYPHEPVATTU, NGUONCAP } from '../../../constant/dbCollections';
import mongoosePaginate from 'mongoose-paginate-v2';
import { TRANG_THAI_VAT_TU } from '../../../constant/constant';

const schema = new Schema({
  id_danh_diem: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: DANHDIEMVATTU,
  },
  serial: { type: String },
  so_giay_phep: {type: String},
  dong_bo_kem_theo: { type: String},
  id_nguon_cap: {
    type: mongoose.Schema.Types.ObjectId,
    ref: NGUONCAP,
  },
  flag: {
    type: String,
    enum: Object.values(TRANG_THAI_VAT_TU),
    default: TRANG_THAI_VAT_TU.CHUA_SU_DUNG,
  },
  ngay_cap: { type: Date },
  han_su_dung: { type: Date },
  nuoc_san_xuat: { type: String },
  so_lo: { type: String },
  isActive: { type: Boolean, default: true },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  versionKey: false,
});

schema.plugin(mongoosePaginate);

export { schema as DocumentSchema };
export default mongoose.model(VATTU, schema);
