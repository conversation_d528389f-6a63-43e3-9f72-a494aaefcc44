import mongoose, { Schema } from 'mongoose';
import { KHOVATTU, THANHLY, THANHLY_CHITIET, TINHTRANG, VATTU } from '../../../../constant/dbCollections';
import mongoosePaginate from 'mongoose-paginate-v2';

const schema = new Schema({
  id_thanhly: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: THANHLY,
  },
  id_vattu: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: VATTU,
  },
  id_kho: {
    type: mongoose.Schema.Types.ObjectId,
    required: false,
    ref: KHOVATTU,
  },
  id_tinhtrang: {
    type: mongoose.Schema.Types.ObjectId,
    // required: false,
    ref: TINHTRANG,
  },
  ghichu: { type: String },
  soluong: { type: Number, required: true },
  isActive: { type: Boolean, default: true },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  versionKey: false,
});
schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(THANHLY_CHITIET, schema);
