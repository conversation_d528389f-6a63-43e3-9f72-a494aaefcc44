import express from 'express';
import passport from 'passport';
import * as tonkhoController from './tonkho.controller';

export const tonkhoRouter = express.Router();

tonkhoRouter
  .route('/danhdiem')
  .get(passport.authenticate('jwt', { session: false }), tonkhoController.getTonKhoDanhDiem)

tonkhoRouter
  .route('/vattu')
  .get(passport.authenticate('jwt', { session: false }), tonkhoController.getTonKhoMaVatTu)

tonkhoRouter
  .route('/donvi')
  .get(passport.authenticate('jwt', { session: false }), tonkhoController.getTonKhoDonVi)



