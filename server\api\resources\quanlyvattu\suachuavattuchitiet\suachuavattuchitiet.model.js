import mongoose, { Schema } from 'mongoose';
import { DONVI, KHOVATTU, SUACHUA, SUACHUA_CHITIET, TINHTRANG, VATTU } from '../../../constant/dbCollections';
import mongoosePaginate from 'mongoose-paginate-v2';

const schema = new Schema({
  id_suachua: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: SUACHUA,
  },
  id_vattu: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: VATTU,
  },
  id_kho: {
    type: mongoose.Schema.Types.ObjectId,
    ref: KHOVATTU,
  },
  id_tinhtrang: {
    type: mongoose.Schema.Types.ObjectId,
    ref: TINHTRANG,
    // required: true,
  },
  ghichu: { type: String },
  soluong: { type: Number, required: true },
  isActive: { type: Boolean, default: true },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  versionKey: false,
});
schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(SUACHUA_CHITIET, schema);
