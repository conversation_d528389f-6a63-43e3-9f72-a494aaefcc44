import * as ValidatorHelper from '../../../helpers/validatorHelper';
import GIAOVATTU_CANHAN from './model';
const Joi = require('joi');

const objSchema = Joi.object({
  id_donvi: Joi.string().required().messages(ValidatorHelper.messageDefine('Đơn vị')),
  thoi_gian_ban_giao_ca_nhan: Joi.date().required().messages(ValidatorHelper.messageDefine('Ngày bàn giao cá nhân')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

export async function getAll(query) {
  return GIAOVATTU_CANHAN.find(query).lean();
}
