import * as ValidatorHelper from '../../../helpers/validatorHelper';
import DANHDIEMVATTU from './danhdiem.model';

export async function getAll(query) {
  return DANHDIEMVATTU.find(query).lean();
}

const Joi = require('joi');

const objSchema = Joi.object({
  ma_danh_diem: Joi.string().required().messages(ValidatorHelper.messageDefine('Mã danh điểm')),
  ten_danh_diem: Joi.string().required().messages(ValidatorHelper.messageDefine('Tên danh điểm')),
  quan_ly_theo_serial: Joi.boolean().messages(ValidatorHelper.messageDefine('Quản lý theo serial')),
  loai_vat_tu_id: Joi.string().required().messages(ValidatorHelper.messageDefine('Loại vật tư')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}
