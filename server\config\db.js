import mongoose from 'mongoose';
import { getConfig } from './config';
import initialiseDatabase from '../api/helpers/seeder/index';

const config = getConfig(process.env.NODE_ENV);
mongoose.Promise = global.Promise;
export const connect = () => {
  mongoose.connect(config.MONGO_URI).then(async () => {
    console.log('Successfully connected to MongoDB');
    await initialiseDatabase();
  }).catch(err => {
    console.log('Not connected to the database: ' + err);
  });
};
