import momentTimezone from 'moment-timezone';
import { TRANG_THAI } from '../constant/constant';

export function formatDateTime(date) {
  return momentTimezone(date).tz('Etc/GMT-7').format('DD-MM-YYYY HH:mm');
}

export function formatDate(date) {
  return momentTimezone(date).tz('Etc/GMT-7').format('DD-MM-YYYY');
}

export function formatToDateDetail(date) {
  return {
    ngay: momentTimezone(date).tz('Etc/GMT-7').format('DD'),
    thang: momentTimezone(date).tz('Etc/GMT-7').format('MM'),
    nam: momentTimezone(date).tz('Etc/GMT-7').format('YYYY'),
    gio: momentTimezone(date).tz('Etc/GMT-7').format('HH'),
    phut: momentTimezone(date).tz('Etc/GMT-7').format('mm'),
    date: momentTimezone(date).tz('Etc/GMT-7').format('DD/MM/YYYY'),
    time: momentTimezone(date).tz('Etc/GMT-7').format('HH giờ mm'),
  };
}


export function groupBy(xs, key) {
  return xs.reduce(function(rv, x) {
    (rv[x[key]] = rv[x[key]] || []).push(x);
    return rv;
  }, {});
}

export function createEventQuery(tgDauky, tgCuoiky, trangThai = [TRANG_THAI.THUC_HIEN_THANH_CONG]) {
  let queryEvent = {};
  if (tgDauky || tgCuoiky) {
    queryEvent.thoi_gian_hoan_thanh = {};
    if (tgCuoiky) {
      queryEvent.thoi_gian_hoan_thanh.$lte = new Date(tgCuoiky);
    }
    if (tgDauky) {
      queryEvent.thoi_gian_hoan_thanh.$gte = new Date(tgDauky);
    }
  }
  queryEvent.is_deleted = false;
  queryEvent.trang_thai = { $in: trangThai };
  return queryEvent;
}
