import * as responseAction from '../../../utils/responseAction';
import queryHelper from '../../../helpers/queryHelper';
import * as Service from './vattudauky.service';
import Model from './vattudauky.model';
import VATTU from '../../danhmuc/vattu/vattu.model';
import { TRANG_THAI_VAT_TU } from '../../../constant/constant';
import exelUtils from '../../../utils/exelUtils';

export async function findOne(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findById(id);
    if (!data) {
      return responseAction.error(res, 404, '');
    }
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function remove(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findOneAndUpdate({ _id: id }, { is_deleted: true }, { new: true });
    await VATTU.findOneAndUpdate({ _id: data.id_vattu }, { is_deleted: true }, { new: true });
    if (!data) {
      return responseAction.error(res, 404, '');
    }
    return responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function update(req, res) {
  try {
    const { id } = req.params;

    const { error, value } = Service.validate(req.body);
    if (error) return responseAction.error(res, error, 400);

    const checkExitSerial = await VATTU.findOne({
      id_danh_diem: value.id_danh_diem,
      serial: value.serial,
      is_deleted: false,
      _id: { $ne: value.id_vattu },
    });
    if (checkExitSerial) {
      return responseAction.error(res, { message: '"Số hiệu, kí hiệu" vật tư đã được nhập vui lòng kiểm tra lại' }, 400);
    } else {
      await VATTU.findOneAndUpdate({ _id: value.id_vattu }, {
        serial: value.serial,
        id_danh_diem: value.id_danh_diem,
      }, { new: true }).lean();
    }
    delete value.serial;
    const data = await Model.findOneAndUpdate({ _id: id }, value, { new: true })
      .populate({ path: 'id_vattu', populate: { path: 'id_nguon_cap id_danh_diem' } })
      .populate({ path: 'id_tinh_trang', select: 'ten_tinh_trang' })
      .populate({ path: 'id_danh_diem', populate: 'ma_don_vi_tinh' });
    if (!data) {
      return responseAction.error(res, null, 404);
    }
    return responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function create(req, res) {

  try {
    const { error, value } = Service.validate(req.body);
    if (error) return responseAction.error(res, error, 400);

    const vatTuInfo = await VATTU.findOne({ _id: value.id_vattu }, { id_danh_diem: 1, id_tinh_trang: 1 })
      .populate({ path: 'id_danh_diem', select: 'quan_ly_theo_serial' });

    if (vatTuInfo) {
      let queryCheckExist = { id_vattu: value.id_vattu, is_deleted: false };
      if (!vatTuInfo.id_danh_diem.quan_ly_theo_serial) {
        queryCheckExist.id_tinh_trang = value.id_tinh_trang;
      }
      const checkExist = await Model.find(queryCheckExist, { _id: 1 });
      if (checkExist.length) {
        return responseAction.error(res, { message: 'Vật tư đã được nhập, vui lòng kiểm tra và thử lại' }, 400);
      }
    }
    await VATTU.findOneAndUpdate({ _id: value.id_vattu }, { flag: TRANG_THAI_VAT_TU.DA_SU_DUNG }, { new: true });
    const data = await Model.create(value);
    let dataRtn = await data
      .populate({ path: 'id_vattu', populate: { path: 'id_nguon_cap id_danh_diem' } })
      .populate({ path: 'id_tinh_trang' })
      .populate({ path: 'id_danh_diem', populate: { path: 'ma_don_vi_tinh' } })
      .populate({ path: 'id_don_vi' }).execPopulate();
    return responseAction.success(res, dataRtn);
  } catch (err) {
    console.error(err);
    return responseAction.error(res, err, 500);
  }
}

export async function getAll(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria, options } = query;
    options.populate = [
      { path: 'id_tinh_trang', select: 'ten_tinh_trang' },
      { path: 'id_don_vi', select: 'ten_don_vi' },
      { path: 'id_vattu', populate: { path: 'id_nguon_cap id_danh_diem' } },
      {
        path: 'id_danh_diem', populate: [
          { path: 'ma_don_vi_tinh' },
          { path: 'loai_vat_tu_id', populate: 'nhom_vat_tu_id' },
        ],
      },
    ];
    const data = await Model.paginate(criteria, options);
    return responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

//Import vật tư đầu kỳ
const SHEET_NAMES = {
  VAT_TU_DAU_KY: 'Vật tư đầu kỳ',
  TIEP_NHAN: 'Tiếp nhận',
};

export async function checkImport(req, res) {

  try {
    let filePath = req.files.file.path;
    const sheetData = await exelUtils.transformFile(filePath);
    const resultArray = await checkImportByData(sheetData);
    responseAction.success(res, resultArray);
  } catch (e) {
    console.log(e);
    responseAction.error(res, e);
  }
}

async function checkImportByData(sheetData) {

  function getSheetByName(sheetData, name) {
    return sheetData.find((sheet) => sheet.name.toLowerCase().includes(name.toLowerCase()));
  }

  let resultArray = [];
  resultArray = [...resultArray, await Service.checkImport(getSheetByName(sheetData, SHEET_NAMES.VAT_TU_DAU_KY))];
  return resultArray;
}

export async function importOne(req, res) {

  try {
    const sheetData = req.body;
    const result = await Service.importData(sheetData);
    responseAction.success(res, result);
  } catch (e) {
    console.log(e);
    responseAction.error(res, e);
  }
}

export async function createMany(req, res) {

  try {
    const result = await Service.getDataToCreateManySupplies(res, req.body);
    let data = await Model.create(result);
    data = await Model.populate(data, [
      { path: 'id_vattu', populate: { path: 'id_nguon_cap id_danh_diem' } },
      { path: 'id_tinh_trang' },
      { path: 'id_danh_diem', populate: { path: 'ma_don_vi_tinh' } },
      { path: 'id_don_vi' },
    ]);
    return responseAction.success(res, data);
  } catch (err) {
    console.error(err);
    return responseAction.error(res, err, 500);
  }
}
