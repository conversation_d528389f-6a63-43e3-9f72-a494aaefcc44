import * as ValidatorHelper from '../../../helpers/validatorHelper';
import DONVI from './donvi.model';

const Joi = require('joi');

const objSchema = Joi.object({
  ten_don_vi: Joi.string().required().messages(ValidatorHelper.messageDefine('Tên đơn vị')),
  ma_don_vi: Joi.string().required().messages(ValidatorHelper.messageDefine('Mã đơn vị')),
  id_don_vi_cha: Joi.string().messages(ValidatorHelper.messageDefine('Đơn vị cha')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}
export function getAll(query) {
  return DONVI.find(query).lean();
}
