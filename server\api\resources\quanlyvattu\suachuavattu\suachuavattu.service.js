import * as ValidatorHelper from '../../../helpers/validatorHelper';
import SUACHUA from './suachuavattu.model';

const Joi = require('joi');

const objSchema = Joi.object({
  id_donvi: Joi.string().required().messages(ValidatorHelper.messageDefine('Đơn vị')),
  thoi_gian_thuc_hien: Joi.string().required().messages(ValidatorHelper.messageDefine('Ngày sửa chữa')),
  thoi_gian_hoan_thanh: Joi.string().messages(ValidatorHelper.messageDefine('<PERSON><PERSON><PERSON> hoàn thành')),
});
export async function getAll(query) {
  return SUACHUA.find(query).lean();
}
export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}
