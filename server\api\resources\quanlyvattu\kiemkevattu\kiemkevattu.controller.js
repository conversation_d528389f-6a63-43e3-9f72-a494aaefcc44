import * as responseAction from '../../../utils/responseAction';
import queryHelper from '../../../helpers/queryHelper';
import * as Service from './kiemkevattu.service';
import Model from './kiemkevattu.model';
import * as KiemKeChiTietService from '../kiemkevattuchitiet/kiemkevattuchitiet.service';
import CommonError from '../../../error/CommonError';

export async function findOne(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findOne({ _id: id, is_deleted: false })
      .populate({ path: 'id_donvi', select: 'ten_don_vi' })
      .lean();
    if (!data) {
      return responseAction.error(res, CommonError.NOT_FOUND());
    }
    data.chitiet = await KiemKeChiTietService.getAll({ id_kiemke: data._id, is_deleted: false })
      .populate({ path: 'id_vattu', populate: { path: 'id_danh_diem' } })
      .populate({ path: 'id_tinhtrang' }).lean();
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function remove(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findOneAndUpdate({ _id: id }, { is_deleted: true }, { new: true }).lean();
    if (!data) {
      return responseAction.error(res, CommonError.NOT_FOUND());
    } else {
      await KiemKeChiTietService.removeAll({ id_kiemke: data._id });
      data.chitiet = await KiemKeChiTietService.getAll({ id_kiemke: data._id });
    }
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function update(req, res) {
  try {
    const { id } = req.params;
    const { error, value } = Service.validate(req.body);
    if (error) return responseAction.error(res, error, 400);
    const data = await Model.findByIdAndUpdate(id, value, { new: true }).lean();
    if (!data) {
      return responseAction.error(res, CommonError.NOT_FOUND());
    } else {
      const chitiet = req.body.chitiet || [];
      const chitietUpdate = chitiet.filter(row => row._id);
      await KiemKeChiTietService.updateAll(chitietUpdate);
      let chitietCreate = chitiet.filter(row => !row.hasOwnProperty('_id'));
      chitietCreate.forEach(row => row.id_kiemke = data._id);
      await KiemKeChiTietService.create(chitietCreate);
      data.chitiet = await KiemKeChiTietService.getAll({ id_kiemke: data._id, is_deleted: false })
        .populate({ path: 'id_vattu', populate: { path: 'id_danh_diem' } })
        .populate({ path: 'id_tinhtrang' }).lean();
    }
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function create(req, res) {
  try {
    const { error, value } = Service.validate(req.body);
    if (error) return responseAction.error(res, error, 400);
    let data = await Model.create(value);
    if (data && req.body.chitiet && Array.isArray(req.body.chitiet)) {
      data = data.toObject();
      req.body.chitiet.forEach(row => {
        row.id_kiemke = data._id;
      });
      data.chitiet = await KiemKeChiTietService.create(req.body.chitiet);
    }
    return responseAction.success(res, data);
  } catch (err) {
    console.error(err);
    return responseAction.error(res, err, 500);
  }
}

export async function getAll(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria, options } = query;
    options.populate = [
      { path: 'id_donvi', select: 'ten_don_vi' },
      { path: 'id_nguoi1', select: 'ten_nhan_vien' },
      { path: 'id_nguoi2', select: 'ten_nhan_vien' },
      { path: 'id_nguoi3', select: 'ten_nhan_vien' },
    ];
    const data = await Model.paginate(criteria, options);
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}
