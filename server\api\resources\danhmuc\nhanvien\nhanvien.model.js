import mongoose, { Schema } from 'mongoose';
import { NHANVIEN, DONVI, TOCHUC_QUANLY } from '../../../constant/dbCollections';
import mongoosePaginate from 'mongoose-paginate-v2';

const schema = new Schema({
  ten_nhan_vien: { type: String, required: true, validate: /\S+/ },
  cmnd: { type: String, required: true, validate: /\S+/, unique: true },
  ngay_cap: { type: Date, required: true, validate: /\S+/ },
  noi_cap: { type: String, required: true, validate: /\S+/ },
  id_don_vi: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: TOCHUC_QUANLY,
  },
  sdt: { type: String },
  isActive: { type: Boolean, default: true },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  versionKey: false,
});


schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(NHANVIEN, schema);
