import mongoose, { Schem<PERSON> } from 'mongoose';
import {
  VATTU_DAUKY,
  VATTU,
  KHOVATTU,
  TINHTRANG,
  DONVI,
  TOCHUC_QUANLY,
  DANHDIEMVATTU, NGUONCAP,
} from '../../../constant/dbCollections';
import mongoosePaginate from 'mongoose-paginate-v2';

const schema = new Schema({
  id_danh_diem: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: DANHDIEMVATTU,
  },
  id_vattu: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: VATTU,
  },
  id_don_vi: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: TOCHUC_QUANLY,
  },
  id_tinh_trang: {
    type: mongoose.Schema.Types.ObjectId,
    ref: TINHTRANG,
  },
  id_nguon_cap: {
    type: mongoose.Schema.Types.ObjectId,
    ref: NGUONCAP,
  },
  thoigian_dauky: { type: Date, required: true },
  ghi_chu: { type: String },
  so_luong: { type: Number, required: true },
  isActive: { type: Boolean, default: true },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  versionKey: false,
});
schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(VATTU_DAUKY, schema);
