import express from 'express';
import passport from 'passport';
import * as thanhLyController from './thanhlyvattu.controller';
import { authorizationMiddleware } from '../../rbac/middleware';
import QuanLyVatTuPermisson from '../../rbac/permissions/quanlyvattu/QuanLyVatTuPermisson';

export const thanhlyvattuRouter = express.Router();

thanhlyvattuRouter.use(passport.authenticate('jwt', { session: false }));

thanhlyvattuRouter.post('*', authorizationMiddleware([QuanLyVatTuPermisson.CREATE]));
thanhlyvattuRouter.get('*', authorizationMiddleware([QuanLyVatTuPermisson.READ]));
thanhlyvattuRouter.put('/:id', authorizationMiddleware([QuanLyVatTuPermisson.UPDATE]));
thanhlyvattuRouter.put('/duyet/:id', authorizationMiddleware([QuanLyVatTuPermisson.CONFIRM]));
thanhlyvattuRouter.delete('*', authorizationMiddleware([QuanLyVatTuPermisson.DELETE]));
thanhlyvattuRouter
  .route('/')
  .get( thanhLyController.getAll)
  .post( thanhLyController.create)

thanhlyvattuRouter
  .route('/duyet/:id')
  .put(thanhLyController.update)

thanhlyvattuRouter
  .route('/:id')
  .get(thanhLyController.findOne)
  .delete( thanhLyController.remove)
  .put( thanhLyController.update);
