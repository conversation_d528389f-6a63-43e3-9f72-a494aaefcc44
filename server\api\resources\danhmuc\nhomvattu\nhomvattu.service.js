import * as ValidatorHelper from '../../../helpers/validatorHelper';

const Joi = require('joi');


const objSchema = Joi.object({
  ten_nhom: Joi.string().required().messages(ValidatorHelper.messageDefine('Tên nhóm vật tư')),
  id_nhom_vat_tu: Joi.string().required().messages(ValidatorHelper.messageDefine('Mã nhóm vật tư')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}
