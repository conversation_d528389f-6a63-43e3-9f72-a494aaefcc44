import express from 'express';
import passport from 'passport';
import * as vatTuDauKyController from './controller';

export const router = express.Router();
router
  .route('/')
  .get(passport.authenticate('jwt', { session: false }), vatTuDauKyController.getAll)
  .post(passport.authenticate('jwt', { session: false }), vatTuDauKyController.create)

router
  .route('/:id')
  .get(passport.authenticate('jwt', { session: false }), vatTuDauKyController.findOne)
  .delete(passport.authenticate('jwt', { session: false }), vatTuDauKyController.remove)
  .put(passport.authenticate('jwt', { session: false }), vatTuDauKyController.update);
