import * as ValidatorHelper from '../../../helpers/validatorHelper';
import TIEP_NHAN from './model';
import * as TinhTrangService from '../../danhmuc/tinhtrang/tinhtrang.service';
import * as NguonCapService from '../../danhmuc/nguoncap/nguoncap.service';
import * as DanhDiemService from '../../danhmuc/danhdiemvattu/danhdiem.service';
import * as VatTuService from '../../danhmuc/vattu/vattu.service';
import { checkDate, checkNumber, convertDate } from '../../../helpers/checkDataHelper';
import { createEventQuery, formatDate, groupBy } from '../../../common/functionCommons';
import * as TiepNhanChiTietService from './chitiet/service';
import VatTuModel from '../../danhmuc/vattu/vattu.model';

const Joi = require('joi');

const objSchema = Joi.object({
  id_donvi: Joi.string().required().messages(ValidatorHelper.messageDefine('Đơn vị')),
  id_nguoi1: Joi.string().required().messages(ValidatorHelper.messageDefine('Người tiến hành 1')),
  id_nguoi2: Joi.string().required().messages(ValidatorHelper.messageDefine('Người tiến hành 2')),
  id_nguoi3: Joi.string().required().messages(ValidatorHelper.messageDefine('Người tiến hành 3')),
  thoi_gian_hoan_thanh: Joi.date().required().messages(ValidatorHelper.messageDefine('Thời gian hoàn thành')),
  thoi_gian_thuc_hien: Joi.date().messages(ValidatorHelper.messageDefine('Thời gian thực hiện')),
  nguon_cungcap: Joi.string().required().messages(ValidatorHelper.messageDefine('Nhà cung cấp')),
  diachi: Joi.string().required().messages(ValidatorHelper.messageDefine('Địa chỉ')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

export async function getAll(query) {
  return TIEP_NHAN.find(query)
    .populate('id_donvi id_nguoi1 id_nguoi2 id_nguoi3')
    .lean();
}

const Headers = {
  TEN_VAT_TU: 'Tên VK, VLN, CCHT',
  SERIAL: 'Số hiệu, ký hiệu',
  SO_LO: 'Số lô',
  STT: 'STT',
  SO_GIAY_PHEP: 'Số giấy phép',
  SO_LUONG: 'Số lượng',
  TINH_TRANG_KY_THUAT: 'Tình trạng kỹ thuật',
  NGUON_CAP: 'Nguồn cấp',
  NGAY_CAP: 'Ngày cấp',
  HAN_SU_DUNG: 'Hạn sử dụng',
  NUOC_SAN_XUAT: 'Nước sản xuất',
  DONG_BO_KEM_THEO: 'Đồng bộ kèm theo',
  GHI_CHU: 'Ghi chú',
};

export async function mapNameToId(mapTinhTrang, mapNguonCap, mapDanhDiem, mapVatTu, mapIdVattu = {}) {
  const allNguonCap = await NguonCapService.getAll({ is_deleted: false });
  allNguonCap.forEach(nguonCap => {
    mapNguonCap[nguonCap.ten_nguon_cap] = nguonCap._id;
  });

  const allTinhTrang = await TinhTrangService.getAll({ is_deleted: false });
  allTinhTrang.forEach(tinhTrang => {
    mapTinhTrang[tinhTrang.ten_tinh_trang] = tinhTrang._id;
  });
  const allVatTu = await VatTuService.getAll({ is_deleted: false });
  allVatTu.forEach(vatTu => {
    mapIdVattu[vatTu.serial] = vatTu;
    mapVatTu[vatTu.serial + vatTu.id_danh_diem] = vatTu._id;
  });

  const allDanhDiem = await DanhDiemService.getAll({ is_deleted: false });
  allDanhDiem.forEach(vatTu => {
    mapDanhDiem[vatTu.ten_danh_diem] = vatTu._id;
  });
}

export async function createSuplies(sheetData) {
  const mapTinhTrang = {}, mapNguonCap = {}, mapVatTu = {}, mapDanhDiem = {}, mapIdVattu = {};

  await mapNameToId(mapTinhTrang, mapNguonCap, mapDanhDiem, mapVatTu, mapIdVattu); //Map name to _id

  //Convert dữ liệu trong sheet thành dữ liệu để create/update vật tư
  function convertToDBSupplies(row) {
    return {
      id_danh_diem: mapDanhDiem[row[Headers.TEN_VAT_TU]?.trim()],
      serial: row[Headers.SERIAL],
      so_lo: row[Headers.SO_LO],
      so_giay_phep: row[Headers.SO_GIAY_PHEP],
      ngay_cap: convertDate(row[Headers.NGAY_CAP]),
      han_su_dung: convertDate(row[Headers.HAN_SU_DUNG]),
      nuoc_san_xuat: row[Headers.NUOC_SAN_XUAT],
      dong_bo_kem_theo: row[Headers.DONG_BO_KEM_THEO],
      ghi_chu: row[Headers.GHI_CHU],
      id_nguon_cap: mapNguonCap[row[Headers.NGUON_CAP]],
    };
  }

  //Nếu chưa có vật tư thì thêm mới vật tư theo serial
  const dataToSupplies = sheetData?.map(row => convertToDBSupplies(row)).filter(element => !mapVatTu[element.serial + element.id_danh_diem]);
  await VatTuModel.create(dataToSupplies);

  return sheetData;
}

export async function convertData(sheetData) {
  const mapTinhTrang = {}, mapNguonCap = {}, mapVatTu = {}, mapDanhDiem = {}, mapIdVattu = {};

  await mapNameToId(mapTinhTrang, mapNguonCap, mapDanhDiem, mapVatTu, mapIdVattu); //Map name to _id

  function convertToRow(row) {
    return {
      id_vattu: mapIdVattu[row[Headers.SERIAL]],
      id_tinh_trang: mapTinhTrang[row[Headers.TINH_TRANG_KY_THUAT]?.trim()],
      so_luong: row[Headers.SO_LUONG],
      ghi_chu: row[Headers.GHI_CHU],
    };
  }

  return sheetData?.map(convertToRow);
}

function trimData(dataInput) {
  if (!Array.isArray(dataInput?.rows)) return dataInput;
  dataInput.rows.forEach(row => {
    Object.entries(row).forEach(([key, value]) => {
      row[key] = (!!value && typeof value === 'string') ? value.trim() : value;
    });
  });
  return dataInput;
}

export async function checkImport(sheetData) {
  trimData(sheetData);
  if (!sheetData) return null;
  const { rows } = sheetData;
  const mapTinhTrang = {}, mapNguonCap = {}, mapVatTu = {}, mapDanhDiem = {};
  await mapNameToId(mapTinhTrang, mapNguonCap, mapDanhDiem, mapVatTu);

  function createError(col, error) {
    return { col, error };
  }

  function validateRow(row) {
    let errors = [];

    if (!row[Headers.TEN_VAT_TU]?.trim()) {
      errors = [...errors, createError(Headers.TEN_VAT_TU, 'Thiếu tên VK, VLN, CCHT!')];
    } else {
      if (!mapDanhDiem[row[Headers.TEN_VAT_TU]?.trim()]) {
        errors = [...errors, createError(Headers.TEN_VAT_TU, 'Tên VK, VLN, CCHT không đúng hoặc chưa được tạo')];
      }
    }

    if (!row[Headers.SERIAL]) {
      errors = [...errors, createError(Headers.SERIAL, 'Thiếu số hiệu, ký hiệu!')];
    } else {
      if (mapVatTu[row[Headers.SERIAL] + mapDanhDiem[row[Headers.TEN_VAT_TU]]]) {
        errors = [...errors, createError(Headers.SERIAL, `Vật tư có Số hiệu ký hiệu "${row[Headers.SERIAL]}" đã được tiếp nhận`)];
      }
    }

    // if (!row[Headers.NGUON_CAP]?.trim()) {
    //   errors = [...errors, createError(Headers.NGUON_CAP, 'Thiếu nguồn cấp')];
    // } else {
    if (row[Headers.NGUON_CAP]?.trim() && !mapNguonCap[row[Headers.NGUON_CAP]?.trim()]) {
      errors = [...errors, createError(Headers.NGUON_CAP, 'Nguồn cấp chưa đúng hoặc chưa được tạo!')];
    }
    // }

    if (!row[Headers.TINH_TRANG_KY_THUAT]?.trim()) {
      errors = [...errors, createError(Headers.TINH_TRANG_KY_THUAT, 'Thiếu tình trạng kỹ thuật')];
    } else {
      if (row[Headers.TINH_TRANG_KY_THUAT]?.trim() && !mapTinhTrang[row[Headers.TINH_TRANG_KY_THUAT]?.trim()]) {
        errors = [...errors, createError(Headers.TINH_TRANG_KY_THUAT, 'Tình trạng kỹ thuật không đúng hoặc chưa được tạo!')];
      }
    }

    if (!row[Headers.SO_LUONG] || !checkNumber(row[Headers.SO_LUONG])) {
      errors = [...errors, createError(Headers.SO_LUONG, 'Không đúng hoặc không có')];
    }
    if (!checkDate(row[Headers.NGAY_CAP])) {
      errors = [...errors, createError(Headers.NGAY_CAP, 'Ngày vận hành không đúng, định dạng đúng là DD/MM/YYYY!')];
    }

    if (!checkDate(row[Headers.HAN_SU_DUNG])) {
      errors = [...errors, createError(Headers.HAN_SU_DUNG, 'Hạn sử dụng không đúng, định dạng đúng là DD/MM/YYYY')];
    }

    if (errors.length) {
      row['Lỗi'] = errors;
    } else {
      row['Lỗi'] = null;
    }
    return row;
  }

  sheetData.rows = rows.map(row => validateRow(row));
  return sheetData;
}

export async function getForLyDoTang(tgBatDau, tgKetThuc, idDonvi) {
  const queryTiepNhan = createEventQuery(tgBatDau, tgKetThuc);

  const tiepnhan = (await TIEP_NHAN.find({ ...queryTiepNhan, ...{ id_donvi: idDonvi } })).map(item => item._id);
  let allChitietTiepNhan = await TiepNhanChiTietService.getAll({ id_tiepnhan: { $in: tiepnhan } })
    .populate([
      { path: 'id_vattu', populate: { path: 'id_danh_diem id_nguon_cap', populate: 'ma_don_vi_tinh' } },
      { path: 'id_tiepnhan id_tinhtrang' },
    ]);
  allChitietTiepNhan.forEach(item => {
    item.id_danh_diem = item.id_vattu.id_danh_diem._id;
  });
  const mapDanhDiem = {};
  allChitietTiepNhan.forEach(item => {
    if (mapDanhDiem[item.id_danh_diem]) {
      mapDanhDiem[item.id_danh_diem].so_luong += item.soluong;
    } else {
      mapDanhDiem[item.id_danh_diem] = {};
      mapDanhDiem[item.id_danh_diem].so_luong = item.soluong;
      mapDanhDiem[item.id_danh_diem].loai = item.id_vattu?.id_danh_diem?.ten_danh_diem;
      mapDanhDiem[item.id_danh_diem].nguon_cap = item.id_vattu?.id_nguon_cap?.ten_nguon_cap;
      mapDanhDiem[item.id_danh_diem].don_vi_tinh = item.id_vattu?.id_danh_diem?.ma_don_vi_tinh?.ten_don_vi_tinh.toLowerCase();
      mapDanhDiem[item.id_danh_diem].tinh_trang = item.id_tinhtrang.ten_tinh_trang.toLowerCase();
      mapDanhDiem[item.id_danh_diem].ngay_cap = item.id_tiepnhan.thoi_gian_hoan_thanh ? formatDate(item.id_tiepnhan.thoi_gian_hoan_thanh) : '';
    }
  });
  const groupByDanhDiem = groupBy(allChitietTiepNhan, 'id_danh_diem');
  let lyDoTang = [];
  Object.keys(groupByDanhDiem).forEach(danhdiem => {
    lyDoTang.push({ ...mapDanhDiem[danhdiem], ...{ type: 'tren_cap', stt: '-' } });
  });
  return lyDoTang;
}
