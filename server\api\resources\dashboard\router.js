import express from 'express';
import passport from 'passport';
import * as controller from './controller';

export const dashboardRouter = express.Router();

dashboardRouter
  .route('/thongke-nhanvien')
  .get(passport.authenticate('jwt', { session: false }), controller.getAllNhanVienByDonVi)

dashboardRouter
  .route('/all-event/:id')
  .get(passport.authenticate('jwt', { session: false }), controller.getAllEvent)

dashboardRouter
  .route('/supplies-event/:id')
  .get(passport.authenticate('jwt', { session: false }), controller.getAllEventDetail)

