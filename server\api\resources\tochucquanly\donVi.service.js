import * as ValidatorHelper from '../../helpers/validatorHelper';
import TOCHUC_QUANLY from './donVi.model';
import { Types } from 'mongoose';

export function getAll(query) {
  return TOCHUC_QUANLY.find(query).lean();
}

export async function getDonViQuery(req, querryDonViId = null) {
  const availableDonViIds = await getDonViInScope(req.user.id_don_vi);
  let donViQuery = [];
  if (querryDonViId) {
    if (availableDonViIds.includes(querryDonViId)) {
      donViQuery = await getDonViInScope(querryDonViId);
    } else {
      donViQuery = [Types.ObjectId()];
    }
  } else {
    donViQuery = availableDonViIds;
  }
  return { $in: donViQuery };
}

function createDataTree(dataset, idProperty, parentIdProperty) {
  const hashTable = Object.create(null);
  dataset.forEach(aData => hashTable[aData[idProperty]] = { ...aData, children: [] });
  const dataTree = [];
  dataset.forEach(aData => {
    if (aData[parentIdProperty] && hashTable[aData[parentIdProperty]]) {
      hashTable[aData[parentIdProperty]].children.push(hashTable[aData[idProperty]]);
    } else {
      dataTree.push(hashTable[aData[idProperty]]);
    }
  });
  return dataTree;
}

export function buildTree(allDonVi) {
  let congTyTreeForAdmin = [];
  congTyTreeForAdmin = createDataTree(allDonVi, '_id', 'don_vi_cha_id');
  return congTyTreeForAdmin;
}

export async function getDonViInScope(currentDonViId) {
  const currentDonVi = await TOCHUC_QUANLY.findById(currentDonViId);
  if (!currentDonVi) return [];
  let parentIs = [currentDonVi._id.toString()];
  let donViSet = new Set([...parentIs]);
  while (parentIs.length) {
    const children = await TOCHUC_QUANLY.find({ is_deleted: false, don_vi_cha_id: { $in: parentIs } }).lean();
    parentIs = children.map(child => child._id.toString());
    donViSet = new Set([...donViSet, ...parentIs]);
  }
  return [...donViSet.values()];
}

const Joi = require('joi');

const objSchema = Joi.object({
  ten_don_vi: Joi.string().required().messages(ValidatorHelper.messageDefine('Tên đơn vị')),
  ma_don_vi: Joi.string().required().messages(ValidatorHelper.messageDefine('Mã đơn vị')),
  don_vi_cha_id: Joi.string().messages(ValidatorHelper.messageDefine('Đơn vị cha')),
  duong_day_nong: Joi.string(),
  email: Joi.string(),
  fax: Joi.string(),
  dia_chi: Joi.string(),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}
