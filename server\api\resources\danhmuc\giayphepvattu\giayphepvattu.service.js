import * as ValidatorHelper from '../../../helpers/validatorHelper';

const Joi = require('joi');


const objSchema = Joi.object({
  so_giay_phep: Joi.string().required().messages(ValidatorHelper.messageDefine('Số giấy phép')),
  ngay_cap: Joi.string().required().messages(ValidatorHelper.messageDefine('Ng<PERSON>y cấp')),
  file_giay_phep: Joi.string().required().messages(ValidatorHelper.messageDefine('File giấy phép')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}
