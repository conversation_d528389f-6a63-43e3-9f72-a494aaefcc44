import mongoose, { Schem<PERSON> } from 'mongoose';
import {
  KHOVATTU,
  GIAOVATTU_CANHAN,
  VATTU,
  GIAONHAN_CHITIET,
  TINHTRANG,
  NHANVIEN, DAN<PERSON>IEMVATTU,
} from '../../../../constant/dbCollections';
import mongoosePaginate from 'mongoose-paginate-v2';

const schema = new Schema({
  id_giaonhan: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: GIAOVATTU_CANHAN,
  },

  id_vattu:{
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: VATTU,
  },
  id_nhanvien: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: NHANVIEN,
  },
  id_tinhtrang: {
    type: mongoose.Schema.Types.ObjectId,
    required: false,
    ref: TINHTRANG,
  },
  soluong: { type: Number, required: true },
  ghichu: { type: String },
  isActive: { type: Boolean, default: true },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  versionKey: false,
});
schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(GIAONHAN_CHITIET, schema);
