import express from 'express';
import passport from 'passport';
import * as giayphepvattuController from './giayphepvattu.controller';
import { authorizationMiddleware } from '../../rbac/middleware';
import DanhMucPermission from '../../rbac/permissions/danhmuc/DanhMucPermission';
import { checkTempFolder, multipartMiddleware } from '../../../utils/fileUtils';

export const giayphepvattuRouter = express.Router();

giayphepvattuRouter.use(passport.authenticate('jwt', { session: false }));
giayphepvattuRouter.get('*', authorizationMiddleware([DanhMucPermission.READ]));
giayphepvattuRouter.post('*', authorizationMiddleware([DanhMucPermission.CREATE]));
giayphepvattuRouter.put('*', authorizationMiddleware([DanhMucPermission.UPDATE]));
giayphepvattuRouter.delete('*', authorizationMiddleware([DanhMucPermission.DELETE]));


giayphepvattuRouter
  .route('/')
  .get(giayphepvattuController.getAll)
  .post(checkTempFolder, multipartMiddleware, giayphepvattuController.create)
  .put(checkTempFolder, multipartMiddleware, giayphepvattuController.update);

giayphepvattuRouter
  .route('/:id')
  .get(giayphepvattuController.findOne)
  .delete(giayphepvattuController.remove);

