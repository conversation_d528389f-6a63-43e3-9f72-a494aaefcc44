import express from 'express';
import passport from 'passport';
import * as vatTuDauKyController from './vattudauky.controller';
import { checkTempFolder, multipartMiddleware } from '../../../utils/fileUtils';
import { authorizationMiddleware } from '../../rbac/middleware';
import VatTuDauKyPermission from '../../rbac/permissions/quanlyvattu/VatTuDauKyPermission';
import { createMany } from './vattudauky.controller';

export const vattuDauKyRouter = express.Router();

vattuDauKyRouter.use(passport.authenticate('jwt', { session: false }));

vattuDauKyRouter.post('*', authorizationMiddleware([VatTuDauKyPermission.CREATE]));
vattuDauKyRouter.get('*', authorizationMiddleware([VatTuDauKyPermission.READ]));
vattuDauKyRouter.put('*', authorizationMiddleware([VatTuDauKyPermission.UPDATE]));
vattuDauKyRouter.delete('*', authorizationMiddleware([VatTuDauKyPermission.DELETE]));

vattuDauKyRouter
  .route('/')
  .get(vatTuDauKyController.getAll)
  .post(vatTuDauKyController.create);

vattuDauKyRouter
  .route('/createmany')
  .post(vatTuDauKyController.createMany);

vattuDauKyRouter
  .route('/checkimport')
  .post(checkTempFolder, multipartMiddleware, vatTuDauKyController.checkImport);

vattuDauKyRouter
  .route('/importone')
  .post(checkTempFolder, multipartMiddleware, vatTuDauKyController.importOne);

vattuDauKyRouter
  .route('/:id')
  .get(vatTuDauKyController.findOne)
  .delete(vatTuDauKyController.remove)
  .put(vatTuDauKyController.update);
