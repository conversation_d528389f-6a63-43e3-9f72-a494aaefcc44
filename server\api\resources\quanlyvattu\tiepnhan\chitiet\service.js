import * as ValidatorHelper from '../../../../helpers/validatorHelper';
import TIEP_NHAN_CHITIET from './model';
import * as VatTuService from '../../../danhmuc/vattu/vattu.service';
import VatTuModel from '../../../danhmuc/vattu/vattu.model';
import * as responseHelper from '../../../../helpers/responseHelper';

const Joi = require('joi');

const objSchema = Joi.object({
  id_tiepnhan: Joi.string().required().messages(ValidatorHelper.messageDefine('Tiếp nhận')),
  id_vattu: Joi.string().required().messages(ValidatorHelper.messageDefine('Vật tư')),
  id_tinhtrang: Joi.string().required().messages(ValidatorHelper.messageDefine('Tình trạng')),
  ghichu: Joi.string().required().messages(ValidatorHelper.messageDefine('<PERSON><PERSON> chú')),
  soluong: Joi.date().required().messages(ValidatorHelper.messageDefine('S<PERSON> lượng')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  if (Array.isArray(data)) {
    let validateError = null;
    data.find(itemData => {
      const { error } = schema.validate(itemData, { allowUnknown: true, abortEarly: true });
      if (error) validateError = error;
      return error;
    });
    if (validateError && validateError.details) {
      return { validateError };
    }
    return { value: data };
  } else {
    const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
    if (error && error.details) {
      return { error };
    }
    return { value };
  }
}

export async function create(res, data) {
  const { error, value } = validate(data);
  if (error) throw error;
  let newSupplies = await VatTuService.checkUniqueSupply(res, value);
  const mapSupplies = {};
  newSupplies?.forEach(item => {
    mapSupplies[item.serial] = item._id;
  });
  value.forEach(item => {
    item.id_vattu = mapSupplies[item.serial] || item.id_vattu._id;
    item.soluong = item.so_luong;
  });
  return TIEP_NHAN_CHITIET.create(value);
}


export async function updateAll(chitietUpdate) {
  function convertToNewSupply(tiepNhan) {
    return {
      id_danh_diem: tiepNhan?.danh_diem_id?._id || tiepNhan?.danh_diem_id,
      serial: tiepNhan.serial,
      so_giay_phep: tiepNhan.so_giay_phep,
      dong_bo_kem_theo: tiepNhan.dong_bo_kem_theo,
      id_nguon_cap: tiepNhan.id_nguon_cap._id || tiepNhan.id_nguon_cap,
      ngay_cap: tiepNhan.ngay_cap,
      han_su_dung: tiepNhan.han_su_dung,
      nuoc_san_xuat: tiepNhan.nuoc_san_xuat,
      so_lo: tiepNhan.so_lo,
    };
  }

  const suppliesToCreate = chitietUpdate.filter(item => !item.id_vattu).map(convertToNewSupply);

  let newSupplies = await VatTuModel.create(suppliesToCreate);

  let existSupplies = [];
  chitietUpdate.forEach(item => {
    if (item.id_vattu) {
      item.id_vattu.id_nguon_cap = item.id_nguon_cap._id;
      item.id_vattu.han_su_dung = item.han_su_dung;
      item.id_vattu.ngay_cap = item.ngay_cap;
      item.id_vattu.nuoc_san_xuat = item.nuoc_san_xuat;
      item.id_vattu.dong_bo_kem_theo = item.dong_bo_kem_theo;
      item.id_vattu.id_danh_diem = item.danh_diem_id;
      existSupplies = [...existSupplies, item.id_vattu];
    }
  });
  await VatTuModel.bulkWrite(
    existSupplies.map((element) =>
      ({
        updateOne: {
          filter: { _id: element._id },
          update: { $set: element },
          upsert: true,
        },
      }),
    ),
  );
  let allSupplies = [];
  if (newSupplies) {
    allSupplies = [...allSupplies, ...newSupplies];
  }
  allSupplies = [...allSupplies, ...existSupplies];
  const mapSupply = {};
  allSupplies.forEach(item => {
    mapSupply[item.serial] = item._id;
  });


  function convertToTiepNhan(value) {
    return {
      _id: value?._id,
      id_vattu: mapSupply[value.serial],
      id_tinhtrang: value.id_tinhtrang?._id,
      soluong: value.so_luong,
      ghichu: value.ghi_chu,
    };
  }

  const dataToUpdate = chitietUpdate.map(convertToTiepNhan);

  return await TIEP_NHAN_CHITIET.bulkWrite(
    dataToUpdate.map((element) =>
      ({
        updateOne: {
          filter: { _id: element._id },
          update: { $set: element },
          upsert: true,
        },
      }),
    ),
  );
}

export function getAll(query) {
  return TIEP_NHAN_CHITIET.find(query)
    .populate('id_vattu')
    .lean();
}

export function getAllWithInfo(query) {
  return TIEP_NHAN_CHITIET.find(query)

    .populate({ path: 'id_tiepnhan', populate: 'id_donvi id_nguoi1 id_nguoi2 id_nguoi3' })
    .lean();
}

export async function removeAll(query) {
  return TIEP_NHAN_CHITIET.updateMany(query, { is_deleted: true });
}

export function getForTonKho(query) {
  return TIEP_NHAN_CHITIET.find(query)
    .populate('id_tiepnhan')
    .lean();
}
