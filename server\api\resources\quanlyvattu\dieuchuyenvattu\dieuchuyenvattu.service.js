import * as ValidatorHelper from '../../../helpers/validatorHelper';
import DIEUCHUYENVATTU from './dieuchuyenvattu.model';
import * as DieuChuyenChiTietService from './dieuchuyenvattuchitiet/dieuchuyenvattuchitiet.service';
import { createEventQuery, formatDate, groupBy } from '../../../common/functionCommons';

const Joi = require('joi');

const objSchema = Joi.object({
  id_donvi_di: Joi.string().required().messages(ValidatorHelper.messageDefine('Đơn vị thực hiện')),
  id_nguoi_di: Joi.string().required().messages(ValidatorHelper.messageDefine('Người thực hiện')),
  id_donvi_den: Joi.string().messages(ValidatorHelper.messageDefine('Đơn vị tiếp nhận')),
  id_nguoi_den: Joi.string().required().messages(ValidatorHelper.messageDefine('Người tiếp nhập')),
  thoi_gian_yeu_cau: Joi.date().required().messages(ValidatorHelper.messageDefine('Ngày yêu cầu')),
  ly_do: Joi.string().required().messages(ValidatorHelper.messageDefine('Lý do')),
  trang_thai: Joi.string().required().messages(ValidatorHelper.messageDefine('Trạng thái')),
  phan_loai: Joi.string().required().messages(ValidatorHelper.messageDefine('Phân loại')),
  lanh_dao_duyet: Joi.boolean().required().messages(ValidatorHelper.messageDefine('Lãnh đạo duyệt')),
  dia_diem_ban_giao: Joi.string().required().messages(ValidatorHelper.messageDefine('Địa điểm bàn giao')),
  thoi_gian_thuc_hien: Joi.string().required().messages(ValidatorHelper.messageDefine('Ngày thực hiện')),
  can_cu: Joi.string().messages(ValidatorHelper.messageDefine('Căn cứ')),
  hoan_thanh: Joi.boolean().required().messages(ValidatorHelper.messageDefine('Hoàn thành')),
});

export async function validateTonkho(data) {
  const allChitiet = await DieuChuyenChiTietService.getAll({ id_dieu_chuyen: data._id });
  return await DieuChuyenChiTietService.checkTonKho(data.id_donvi_di._id, allChitiet);
}

export async function getAll(query) {
  return DIEUCHUYENVATTU.find(query)
    .populate({ path: 'id_donvi_di', select: 'ten_don_vi' })
    .populate({ path: 'id_donvi_den', select: 'ten_don_vi' })
    .populate({ path: 'id_nguoi_di' })
    .populate({ path: 'id_nguoi_den' }).lean();
}

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

export async function getForLyDoTang(tgBatDau, tgKetThuc, idDonvi) {
  const queryDieuChuyen = createEventQuery(tgBatDau, tgKetThuc);
  if (idDonvi) {
    queryDieuChuyen.id_donvi_den = idDonvi;
  }
  const dieuchuyen = (await DIEUCHUYENVATTU.find(queryDieuChuyen)).map(item => item._id);
  let allChitiet = await DieuChuyenChiTietService.getAll({ id_dieu_chuyen: { $in: dieuchuyen } })
    .populate([
      { path: 'id_dieu_chuyen', populate: 'id_donvi_di' },
      { path: 'id_vattu', populate: { path: 'id_danh_diem', populate: 'ma_don_vi_tinh' } },
    ]);
  allChitiet.forEach(item => {
    item.id_danh_diem = item.id_vattu.id_danh_diem._id;
  });
  const groupByDanhDiem = groupBy(allChitiet, 'id_danh_diem');

  const mapDanhDiem = {};
  allChitiet.forEach(item => {
    if (!mapDanhDiem[item.id_danh_diem]) {
      mapDanhDiem[item.id_danh_diem] = {
        ...{
          so_luong: item.soluong,
          loai: item.id_vattu.id_danh_diem.ten_danh_diem,
          don_vi_tinh: item.id_vattu?.id_danh_diem?.ma_don_vi_tinh?.ten_don_vi_tinh?.toLowerCase(),
          don_vi_chuyen: item.id_dieu_chuyen.id_donvi_di.ten_don_vi,
          ngay_chuyen: item.id_dieu_chuyen.thoi_gian_hoan_thanh ? formatDate(item.id_dieu_chuyen.thoi_gian_hoan_thanh) : '',
        },
      };
    } else {
      mapDanhDiem[item.id_danh_diem].so_luong += item.soluong;
    }
  });
  let lyDoTang = [];
  Object.keys(groupByDanhDiem).forEach(danhdiem => {
    lyDoTang.push({ ...mapDanhDiem[danhdiem], ...{ type: 'chuyen_den', stt: '-' } });
  });
  return lyDoTang;
}

export async function getForLyDoGiam(tgBatDau, tgKetThuc, idDonvi) {
  const queryDieuChuyen = createEventQuery(tgBatDau, tgKetThuc);
  if (idDonvi) {
    queryDieuChuyen.id_donvi_di = idDonvi;
  }
  const dieuchuyen = (await DIEUCHUYENVATTU.find(queryDieuChuyen)).map(item => item._id);
  let allChitiet = await DieuChuyenChiTietService.getAll({ id_dieu_chuyen: { $in: dieuchuyen } })
    .populate([
      { path: 'id_dieu_chuyen', populate: 'id_donvi_di id_donvi_den' },
      { path: 'id_vattu', populate: { path: 'id_danh_diem', populate: 'ma_don_vi_tinh' } },
    ]);
  allChitiet.forEach(item => {
    item.id_danh_diem = item.id_vattu.id_danh_diem._id;
  });
  const groupByDanhDiem = groupBy(allChitiet, 'id_danh_diem');

  const mapDanhDiem = {};
  allChitiet.forEach(item => {
    if (mapDanhDiem[item.id_danh_diem]) {
      mapDanhDiem[item.id_danh_diem].so_luong += item.soluong;
    } else {
      mapDanhDiem[item.id_danh_diem] = {
        ...{
          so_luong: item.soluong,
          loai: item.id_vattu.id_danh_diem.ten_danh_diem,
          don_vi_tinh: item.id_vattu.id_danh_diem?.ma_don_vi_tinh?.ten_don_vi_tinh?.toLowerCase(),
          don_vi_nhan: item.id_dieu_chuyen.id_donvi_den.ten_don_vi,
          ngay_chuyen: item.id_dieu_chuyen.thoi_gian_hoan_thanh ? formatDate(item.id_dieu_chuyen.thoi_gian_hoan_thanh) : '',
        },
      };
    }
  });
  let lyDoGiam = [];
  Object.keys(groupByDanhDiem).forEach(danhdiem => {
    lyDoGiam.push({ ...mapDanhDiem[danhdiem], ...{ stt: '-', type: 'chuyen_di' } });
  });
  return lyDoGiam;
}
