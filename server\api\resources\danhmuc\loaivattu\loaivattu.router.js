import express from 'express';
import passport from 'passport';
import * as loaiVattuController from './loaivattu.controller';
import { authorizationMiddleware } from '../../rbac/middleware';
import DanhMucPermission from '../../rbac/permissions/danhmuc/DanhMucPermission';

export const loaiVattuRouter = express.Router();

loaiVattuRouter.use(passport.authenticate('jwt', { session: false }));
loaiVattuRouter.get('*', authorizationMiddleware([DanhMucPermission.READ]));
loaiVattuRouter.post('*', authorizationMiddleware([DanhMucPermission.CREATE]));
loaiVattuRouter.put('*', authorizationMiddleware([DanhMucPermission.UPDATE]));
loaiVattuRouter.delete('*', authorizationMiddleware([DanhMucPermission.DELETE]));

loaiVattuRouter
  .route('/')
  .get(loaiVattuController.getAll)
  .post(loaiVattuController.create);

loaiVattuRouter
  .route('/:id')
  .get(loaiVattuController.findOne)
  .delete(loaiVattuController.remove)
  .put(loaiVattuController.update);
