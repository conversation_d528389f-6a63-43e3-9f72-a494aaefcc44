import express from 'express';
import passport from 'passport';
import * as suachuaController from './suachuavattu.controller';

export const suachuavattuRouter = express.Router();
suachuavattuRouter
  .route('/')
  .get(passport.authenticate('jwt', { session: false }), suachuaController.getAll)
  .post(passport.authenticate('jwt', { session: false }), suachuaController.create)

suachuavattuRouter
  .route('/:id')
  .get(passport.authenticate('jwt', { session: false }), suachuaController.findOne)
  .delete(passport.authenticate('jwt', { session: false }), suachuaController.remove)
  .put(passport.authenticate('jwt', { session: false }), suachuaController.update);
