import * as responseAction from '../../../utils/responseAction';
import * as Service from './service';
import queryHelper from '../../../helpers/queryHelper';
import Model from '../../danhmuc/vattu/vattu.model';
import DANHDIEMVATTU from '../../danhmuc/danhdiemvattu/danhdiem.model';
import { TRANG_THAI_VAT_TU } from '../../../constant/constant';
import * as VatTuDauKyService from '../vattudauky/vattudauky.service';
import * as TiepNhanChiTietService from '../tiepnhan/chitiet/service';


export const getLichSuVattu = async (req, res) => {
  try {
    const { id } = req.params;
    const lichSuVattu = await Service.getLichSuVattu(id);
    responseAction.success(res, lichSuVattu);
  } catch (err) {
    responseAction.error(res, err);
  }
};
export const getLichSuEventTheoDonVi = async (req, res) => {
  try {
    const { id } = req.params;
    const lichSuEvent = await Service.getLichSuEventTheoDonVi(id);
    responseAction.success(res, lichSuEvent);
  } catch (err) {
    responseAction.error(res, err);
  }
};

export async function getAllLichSuVuKhi(req, res) {
  try {
    if (!req.query.id_danh_diem) {
      const allDanhDiemVuKhi = await DANHDIEMVATTU.find({
        quan_ly_theo_serial: true,
        is_deleted: false,
      }, { _id: 1 });
      req.query.id_danh_diem = allDanhDiemVuKhi.map(danhDiem => danhDiem._id);
    }
    const dataDauKy = await VatTuDauKyService.getAll();
    const dataTiepNhan = await TiepNhanChiTietService.getAll();
    let existIds = [];
    for (let item of dataDauKy) {
      existIds = [...existIds, item];
    }
    for (let item of dataTiepNhan) {
      existIds = [...existIds, item];
    }

    const query = queryHelper.extractQueryParam(req);
    const { criteria, options } = query;

    options.populate = [
      { path: 'id_danh_diem', populate: 'ma_don_vi_tinh' },
      { path: 'id_nguon_cap', select: 'ten_nguon_cap' },
    ];
    criteria._id = {
      $in: existIds.map(item => item.id_vattu),
    };
    const data = await Model.paginate(criteria, options);
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}


