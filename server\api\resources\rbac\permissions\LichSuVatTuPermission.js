import {create} from "../permissionHelper";
import resources from "../Resources";
import actions from "../Actions";

export default {
  CREATE: create(resources.HISTORY_SUPPLIES, actions.CREATE),
  UPDATE: create(resources.HISTORY_SUPPLIES, actions.UPDATE),
  CONFIRM: create(resources.HISTORY_SUPPLIES, actions.CONFIRM),
  SELECT: create(resources.HISTORY_SUPPLIES, actions.SELECT),
  READ: create(resources.HISTORY_SUPPLIES, actions.READ),
  DELETE: create(resources.HISTORY_SUPPLIES, actions.DELETE)
}
