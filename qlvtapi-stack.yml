version: "3.2"
services:
  qlvtapi:
    image: "registry.thinklabs.com.vn:5000/qlvtapi:latest"
    deploy:
      replicas: 1
      placement:
        constraints: [node.labels.environment==development]
      restart_policy:
        condition: any
    environment:
      PORT: 3001
      NODE_ENV: "production"
      SERVICE_3001_NAME: "qlvtapi"
      SERVICE_NAME: "qlvtapi"
      SERVICE_TAGS: "qlvtapi"
    ports:
      - target: 3001
        published: 4010
        mode: host
    volumes:
      - storage:/usr/src/app/storage
volumes:
  storage:
    driver: lizardfs
