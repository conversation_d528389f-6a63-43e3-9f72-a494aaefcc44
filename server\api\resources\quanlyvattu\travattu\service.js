import * as ValidatorHelper from '../../../helpers/validatorHelper';
import TRA_VATTU from './model';
const Joi = require('joi');

const objSchema = Joi.object({
  id_donvi: Joi.string().required().messages(ValidatorHelper.messageDefine('Đơn vị')),
  thoi_gian_tra_vat_tu: Joi.date().required().messages(ValidatorHelper.messageDefine('Ngày hoàn trả vật tư')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

export async function getAll(query) {
  return TRA_VATTU.find(query).lean();
}
