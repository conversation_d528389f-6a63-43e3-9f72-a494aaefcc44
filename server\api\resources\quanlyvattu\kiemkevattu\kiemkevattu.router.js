import express from 'express';
import passport from 'passport';
import * as kiemkevattuController from './kiemkevattu.controller';

export const kiemkevattuRouter = express.Router();
kiemkevattuRouter
  .route('/')
  .get(passport.authenticate('jwt', { session: false }), kiemkevattuController.getAll)
  .post(passport.authenticate('jwt', { session: false }), kiemkevattuController.create)

kiemkevattuRouter
  .route('/:id')
  .get(passport.authenticate('jwt', { session: false }), kiemkevattuController.findOne)
  .delete(passport.authenticate('jwt', { session: false }), kiemkevattuController.remove)
  .put(passport.authenticate('jwt', { session: false }), kiemkevattuController.update);
