// const DB_DEV_URI = "mongodb://localhost:27017";
// const DB_PROD_URI = "mongodb://localhost:27017";
const DB_DEV_URI = "******************************************************************************************************";
const DB_PROD_URI = "******************************************************************************************************";

const config = {
  production: {
    secret: 'qlvtca',
    MONGO_URI: process.env.MONGO_URI || DB_PROD_URI,
    port: process.env.PORT,
    "cos": {
      "credentials": {
        "serviceInstanceId": "crn:v1:bluemix:public:cloud-object-storage:global:a/c8892a299ea04a629bf4ed7f5ed8a5b1:cbf00124-96fd-48d5-be8f-f195a70420b2::",
        "endpoint": "https://s3.ap.cloud-object-storage.appdomain.cloud",
        "apiKeyId": "rki429NHwsuGE2zwKLWdgDUoJvK7EJtrpdrXCG0-wPSV",
        "ibmAuthEndpoint": "https://iam.ng.bluemix.net/oidc/token",
      },
      "bucketName": "aibolit-collect"
    },
    "mail": {
      "host": "smtp.gmail.com",
      "port": 587,
      "secure": false,
      "auth": {
        "user": "<EMAIL>",
        "pass": "thinklabs@36"
      }
    },
    host_admin: 'https://311-admin.mybluemix.net',
    host_citizen: 'https://311-web.mybluemix.net',
    mail_mailgun: {
      "auth": {
        "api_key": "**************************************************",
        "domain": "sandboxd48b21c0aea74e508097ca22c1ac3ad7.mailgun.org"
      }
    }
  },
  development: {
    secret: 'qlvtca',
    MONGO_URI: process.env.MONGO_URI || DB_DEV_URI,
    port: 27017,
    "cos": {
      "credentials": {
        "serviceInstanceId": "crn:v1:bluemix:public:cloud-object-storage:global:a/c8892a299ea04a629bf4ed7f5ed8a5b1:cbf00124-96fd-48d5-be8f-f195a70420b2::",
        "endpoint": "https://s3.ap.cloud-object-storage.appdomain.cloud",
        "apiKeyId": "rki429NHwsuGE2zwKLWdgDUoJvK7EJtrpdrXCG0-wPSV",
        "ibmAuthEndpoint": "https://iam.ng.bluemix.net/oidc/token",
      },
      "bucketName": "aibolit-collect"
    },
    "mail": {
      "host": "smtp.gmail.com",
      "port": 587,
      "secure": false,
      "auth": {
        "user": "<EMAIL>",
        "pass": "thinklabs@36"
      }
    },
    host_admin: 'http://localhost:8088',
    host_citizen: 'http://localhost:8081',
    mail_mailgun: {
      "auth": {
        "api_key": "**************************************************",
        "domain": "thinklabs.vn"
      }
    }
  },
};

export const getConfig = env => config[env] || config.development;
