import mongoose, { Schema } from 'mongoose';
import { GIAYPHEPVATTU } from '../../../constant/dbCollections';
import mongoosePaginate from 'mongoose-paginate-v2';

const schema = new Schema({
  so_giay_phep: { type: String, required: true, validate: /\S+/, unique: true, index: true },
  ngay_cap: { type: Date, required: true },
  file_giay_phep: { type: String, required: true, validate: /\S+/ },
  file_id: { type: String },
  file_name: { type: String },
  isActive: { type: Boolean, default: true },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  versionKey: false,
});

schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(GIAYPHEPVATTU, schema);
