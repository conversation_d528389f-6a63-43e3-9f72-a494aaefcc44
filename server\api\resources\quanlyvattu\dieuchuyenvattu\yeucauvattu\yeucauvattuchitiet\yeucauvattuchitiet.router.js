import express from 'express';
import passport from 'passport';
import * as yeucauvattuchitietController from './yeucauvattuchitiet.controller';

export const yeucauvattuchitietRouter = express.Router();
yeucauvattuchitietRouter
  .route('/')
  .get(passport.authenticate('jwt', { session: false }), yeucauvattuchitietController.getAll)
  .post(passport.authenticate('jwt', { session: false }), yeucauvattuchitietController.create)

yeucauvattuchitietRouter
  .route('/:id')
  .get(passport.authenticate('jwt', { session: false }), yeucauvattuchitietController.findOne)
  .delete(passport.authenticate('jwt', { session: false }), yeucauvattuchitietController.remove)
  .put(passport.authenticate('jwt', { session: false }), yeucauvattuchitietController.update);
