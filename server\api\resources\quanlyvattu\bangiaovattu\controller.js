import * as responseAction from '../../../utils/responseAction';
import * as DieuChuyenController from '../dieuchuyenvattu/dieuchuyenvattu.controller';
import { LOAI_DIEU_CHUYEN } from '../../../constant/constant';

export async function findOne(req, res) {
  try {
    req.query.phan_loai = LOAI_DIEU_CHUYEN.BAN_GIAO;
    return DieuChuyenController.findOne(req, res);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function remove(req, res) {
  try {
    req.query.phan_loai = LOAI_DIEU_CHUYEN.BAN_GIAO;
    return DieuChuyenController.remove(req, res);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function update(req, res) {
  try {
    req.query.phan_loai = LOAI_DIEU_CHUYEN.BAN_GIAO;
    req.body.phan_loai = LOAI_DIEU_CHUYEN.BAN_GIAO;
    return DieuChuyenController.update(req, res);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function create(req, res) {
  try {
    req.body.phan_loai = LOAI_DIEU_CHUYEN.BAN_GIAO;
    return DieuChuyenController.create(req, res);
  } catch (err) {
    console.error(err);
    return responseAction.error(res, err, 500);
  }
}

export async function getAll(req, res) {
  try {
    req.query.phan_loai = LOAI_DIEU_CHUYEN.BAN_GIAO;
    return DieuChuyenController.getAll(req, res);
  } catch (err) {
    responseAction.error(res, err);
  }
}
