import mongoose, { Schem<PERSON> } from 'mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';
import { LOAIVATTU , NHOMVATTU} from '../../../constant/dbCollections';

const schema = new Schema({
  ten_loai: { type: String, required: true, validate: /\S+/ },
  ma_loai: { type: String, required: true, validate: /\S+/, unique:true },
  nhom_vat_tu_id: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: NHOMVATTU,
  },
  isActive: { type: Boolean, default: true },
  is_deleted: { type: Boolean, default: false, select: false },
});
schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(LOAIVATTU, schema);
