import mongoose, { Schema } from 'mongoose';
import { DONVI } from '../../../constant/dbCollections';
import mongoosePaginate from 'mongoose-paginate-v2';

const schema = new Schema({
  ten_don_vi: { type: String, required: true, validate: /\S+/ },
  ma_don_vi: { type: String, required: true},
  id_don_vi_cha: {
    type: mongoose.Schema.Types.ObjectId,
    ref: DONVI
  },
  isActive: { type: Boolean, default: true },
  is_deleted: { type: Boolean, default: false },
}, {
  versionKey: false,
});

schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(DONVI, schema);
