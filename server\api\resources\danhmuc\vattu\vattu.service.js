import * as ValidatorHelper from '../../../helpers/validatorHelper';
import VATTU from './vattu.model';
import * as responseHelper from '../../../helpers/responseHelper';

export async function getAll(query) {
  return VATTU.find(query).lean();
}


const Joi = require('joi');

const objSchema = Joi.object({
  ten_vat_tu: Joi.string().required().messages(ValidatorHelper.messageDefine('Tên vật tư')),
  dong_bo_kem_theo: Joi.string().required().messages(ValidatorHelper.messageDefine('Đồng bộ kèm theo')),
  ngay_cap: Joi.date().required().messages(ValidatorHelper.messageDefine('Ng<PERSON>y cấp')),
  nuoc_san_xuat: Joi.string().required().messages(ValidatorHelper.messageDefine('Nước sản xuất')),
  serial: Joi.string().required().messages(ValidatorHelper.messageDefine('Serial')),
  han_su_dung: Joi.date().required().messages(ValidatorHelper.messageDefine('Hạn sử dụng')),
  so_lo: Joi.string().required().messages(ValidatorHelper.messageDefine('Số lô')),
});

export async function create(data) {
  return VATTU.create(data);
}

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

export async function checkUniqueSupply(res, dataCreate) {
  let suppliesToCreate = [];
  for (let i = 0; i < dataCreate.length; ++i) {
    let supplies = dataCreate[i];
    const queryId = supplies.id_danh_diem ? { id_danh_diem: { $eq: supplies.id_danh_diem } } : {};
    const checkUniqueSerial = await VATTU.count({
      serial: supplies.serial,
      is_deleted: false, ...queryId,
    });
    if (checkUniqueSerial) {
      return responseHelper.error(res, { message: `Số hiệu, ký hiệu "${supplies.serial}" đã tồn tại, vui lòng kiểm tra và thử lại` }, 404);
    }
    const dataPush = {
      id_danh_diem: dataCreate[i].id_danh_diem || dataCreate[i].danh_diem_id,
      serial: dataCreate[i].serial,
      ngay_cap: dataCreate[i].ngay_cap,
      han_su_dung: dataCreate[i].han_su_dung,
      nuoc_san_xuat: dataCreate[i].nuoc_san_xuat,
      so_lo: dataCreate[i].so_lo,
      so_giay_phep: dataCreate[i].so_giay_phep,
      dong_bo_kem_theo: dataCreate[i].dong_bo_kem_theo,
      id_nguon_cap: dataCreate[i].id_nguon_cap,
    };
    suppliesToCreate.push(dataPush);
  }
  return await VATTU.create(suppliesToCreate);
}
