import * as responseAction from '../../../utils/responseAction';
import queryHelper from '../../../helpers/queryHelper';
import * as Service from './danhdiem.service';
import Model from './danhdiem.model';

export async function findOne(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findById(id);
    if (!data) {
      return responseAction.error(res, 404, '');
    }
    return responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function remove(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findOneAndUpdate({ _id: id }, { is_deleted: true }, { new: true });
    if (!data) {
      return responseAction.error(res, 404, '');
    }
    return responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function update(req, res) {
  try {
    const { id } = req.params;

    const { error, value } = Service.validate(req.body);
    if (error) return responseAction.error(res, error, 400);
    const isUniqueCode = await Model.findOne({ ma_danh_diem: value.ma_danh_diem, is_deleted: false, _id: { $ne: value._id } }, { _id: 1 });
    if (isUniqueCode) {
      return responseAction.error(res, { message: 'Mã danh điểm đã tồn tại, vui lòng kiểm tra và thử lại' }, 400);
    }
    const isUniqueName = await Model.findOne({ ten_danh_diem: value.ten_danh_diem, is_deleted: false, _id: { $ne: value._id } }, { _id: 1 });
    if (isUniqueName) {
      return responseAction.error(res, { message: 'Tên danh điểm đã tồn tại, vui lòng kiểm tra và thử lại' }, 400);
    }
    const data = await Model.findOneAndUpdate({ _id: id }, value, { new: true })
      .populate({path:'loai_vat_tu_id', select:'ten_loai'})
      .populate({ path: 'ma_don_vi_tinh', select: 'ten_don_vi_tinh' })
    if (!data) {
      return responseAction.error(res, null, 404);
    }
    return responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function create(req, res) {

  try {
    const { error, value } = Service.validate(req.body);
    if (error) return responseAction.error(res, error, 400);
    const isUniqueCode = await Model.findOne({ ma_danh_diem: value.ma_danh_diem, is_deleted: false,  }, { _id: 1 });
    if (isUniqueCode) {
      return responseAction.error(res, { message: 'Mã danh điểm đã tồn tại, vui lòng kiểm tra và thử lại' }, 400);
    }
    const isUniqueName = await Model.findOne({ ten_danh_diem: value.ten_danh_diem, is_deleted: false,  }, { _id: 1 });
    if (isUniqueName) {
      return responseAction.error(res, { message: 'Tên danh điểm đã tồn tại, vui lòng kiểm tra và thử lại' }, 400);
    }
    const data = await Model.create(value)
    const dataRtn = await data
      .populate({ path: 'ma_don_vi_tinh', select: 'ten_don_vi_tinh' })
      .populate({path: 'loai_vat_tu_id', select: 'ten_loai'}).execPopulate()
    return responseAction.success(res, dataRtn);
  } catch (err) {
    console.error(err);
    return responseAction.error(res, err, 500);
  }
}

export async function getAll(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria, options } = query;
    options.populate = [
      {path: 'loai_vat_tu_id', select: 'ten_loai'},
      { path: 'ma_don_vi_tinh', select: 'ten_don_vi_tinh' },
    ];
    const data = await Model.paginate(criteria, options);
    return responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}
