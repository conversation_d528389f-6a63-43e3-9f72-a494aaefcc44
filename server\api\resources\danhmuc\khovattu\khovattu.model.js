import mongoose, { Schema } from 'mongoose';
import { KHOVATTU, DONVI } from '../../../constant/dbCollections';
import mongoosePaginate from 'mongoose-paginate-v2';

const schema = new Schema({
  ten_kho: { type: String, required: true, validate: /\S+/},
  ma_kho: { type: String, required: true, validate: /\S+/, unique: true },
  id_don_vi: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: DONVI,
  },
  isActive: { type: Boolean, default: true },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  versionKey: false,
});

schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(KHOVATTU, schema);
