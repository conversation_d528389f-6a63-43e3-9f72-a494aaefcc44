import * as ValidatorHelper from '../../../helpers/validatorHelper';
import LOAIVATTU from './loaivattu.model';

const Joi = require('joi');


const objSchema = Joi.object({
  name: Joi.string().required().messages(ValidatorHelper.messageDefine('Tên loại vật tư')),
  id_loai: Joi.string().required().messages(ValidatorHelper.messageDefine('Id loại vật tư')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}
export async function getAll(query) {
  return LOAIVATTU.find(query).lean()
}
