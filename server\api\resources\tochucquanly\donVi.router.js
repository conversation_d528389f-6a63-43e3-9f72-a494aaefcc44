import express from 'express';
import passport from 'passport';
import * as donviController from './donVi.controller';
import { authorizationMiddleware } from '../rbac/middleware';
import DanhMucPermission from '../rbac/permissions/danhmuc/DanhMucPermission';

export const toChucQuanLyRouter = express.Router();
toChucQuanLyRouter.use(passport.authenticate('jwt', { session: false }));
toChucQuanLyRouter.post('*', authorizationMiddleware([DanhMucPermission.CREATE]));
toChucQuanLyRouter.put('*', authorizationMiddleware([DanhMucPermission.UPDATE]));
toChucQuanLyRouter.delete('*', authorizationMiddleware([DanhMucPermission.DELETE]));
toChucQuanLyRouter
  .route('/')
  .get(donviController.getAll)
  .post(donviController.create);

toChucQuanLyRouter
  .route('/:id')
  .get(donviController.findOne)
  .delete(donviController.remove)
  .put(donviController.update);
