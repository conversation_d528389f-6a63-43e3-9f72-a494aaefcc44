import express from 'express';
import passport from 'passport';
import * as nhomvattuController from './nhomvattu.controller';
import { authorizationMiddleware } from '../../rbac/middleware';
import DanhMucPermission from '../../rbac/permissions/danhmuc/DanhMucPermission';

export const nhomvattuRouter = express.Router();

nhomvattuRouter.use(passport.authenticate('jwt', { session: false }));
nhomvattuRouter.get('*', authorizationMiddleware([DanhMucPermission.READ]));
nhomvattuRouter.post('*', authorizationMiddleware([DanhMucPermission.CREATE]));
nhomvattuRouter.put('*', authorizationMiddleware([DanhMucPermission.UPDATE]));
nhomvattuRouter.delete('*', authorizationMiddleware([DanhMucPermission.DELETE]));
nhomvattuRouter.route('/')
  .get(nhomvattuController.getAll)
  .post(nhomvattuController.create);

nhomvattuRouter
  .route('/:id')
  .get(nhomvattuController.findOne)
  .delete(nhomvattuController.remove)
  .put(nhomvattuController.update);
