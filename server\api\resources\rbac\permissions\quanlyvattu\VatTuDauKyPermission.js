import {create} from "../../permissionHelper";
import resources from "../../Resources";
import actions from "../../Actions";

export default {
  CREATE: create(resources.VATTU_DAUKY, actions.CREATE),
  CONFIRM: create(resources.VATTU_DAUKY, actions.CONFIRM),
  UPDATE: create(resources.VATTU_DAUKY, actions.UPDATE),
  SELECT: create(resources.VATTU_DAUKY, actions.SELECT),
  READ: create(resources.VATTU_DAUKY, actions.READ),
  DELETE: create(resources.VATTU_DAUKY, actions.DELETE)
}
