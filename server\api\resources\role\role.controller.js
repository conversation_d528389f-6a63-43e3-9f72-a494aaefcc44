import Role from './role.model';
import * as responseAction from '../../utils/responseAction';
import queryHelper from '../../helpers/queryHelper';

export async function create(req, res) {
  try {
    const isUnique = await Role.findOne({ code: req.body.code, is_deleted: false }, { _id: 1 });
    if (isUnique) {
      return responseAction.error(res, { message: 'Mã vai trò đã tồn tại, xin vui lòng kiểm tra lại!' }, 400);
    }
    let role = await Role.create(req.body);
    responseAction.success(res, role);
  } catch (e) {
    responseAction.error(res, e);
  }
}

function deleteALl(req, res) {
  Role.remove({}, err => {
    if (err) {
      res.json(err);
    } else {
      res.json('Delete all rolse success');
    }
  });
}

function getAll(req, res) {
  const query = queryHelper.extractQueryParam(req);
  const { criteria } = query;
  Role.find({ $and: [{ code: { $nin: ['CODE_SYSTEM_ADMIN'] } }, criteria] }).exec()
    .then(docs => responseAction.success(res, docs))
    .catch(err => responseAction.error(res, err));
}

export async function UpdateById(req, res) {
  const isUnique = await Role.findOne({ code: req.body.code, is_deleted: false, _id: { $ne: req.body._id } });
  if (isUnique) {
    return responseAction.error(res, { message: 'Mã vai trò đã tồn tại, xin vui lòng kiểm tra lại!' }, 400);
  }
  Role.findByIdAndUpdate(req.params.id, req.body, { new: true, runValidators: true })
    .exec()
    .then(docs => {
      if (docs) {
        responseAction.success(res, docs);
      } else {
        responseAction.error(res, null);
      }
    })
    .catch(err => responseAction.error(res, err));
}

function deleteById(req, res) {
  let idDelete = req.params.id;
  Role.deleteOne({ _id: idDelete })
    .exec()
    .then(docs => responseAction.success(res, { _id: idDelete }))
    .catch(err => responseAction.error(res, err));
}

function getById(req, res) {
  Role.findById(req.params.id).exec()
    .then(docs => responseAction.success(res, docs))
    .catch(err => responseAction.error(res, err));
}

export { deleteALl, getAll, deleteById, getById };
