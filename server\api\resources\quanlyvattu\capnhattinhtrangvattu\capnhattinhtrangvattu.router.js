import express from 'express';
import passport from 'passport';
import * as controller from './capnhattinhtrangvattu.controller';
import { authorizationMiddleware } from '../../rbac/middleware';
import QuanLyVatTuPermisson from '../../rbac/permissions/quanlyvattu/QuanLyVatTuPermisson';

export const capnhattinhtrangvtRouter = express.Router();
capnhattinhtrangvtRouter.use(passport.authenticate('jwt', { session: false }));

capnhattinhtrangvtRouter.post('*', authorizationMiddleware([QuanLyVatTuPermisson.CREATE]));
capnhattinhtrangvtRouter.get('*', authorizationMiddleware([QuanLyVatTuPermisson.READ]));
capnhattinhtrangvtRouter.put('/:id', authorizationMiddleware([QuanLyVatTuPermisson.UPDATE]));
capnhattinhtrangvtRouter.put('/duyet/:id', authorizationMiddleware([QuanLyVatTuPermisson.CONFIRM]));
capnhattinhtrangvtRouter.delete('*', authorizationMiddleware([QuanLyVatTuPermisson.DELETE]));

capnhattinhtrangvtRouter
  .route('/')
  .get(controller.getAll)
  .post(controller.create);

capnhattinhtrangvtRouter
  .route('/duyet/:id')
  .put(controller.update)

capnhattinhtrangvtRouter
  .route('/:id')
  .get(controller.findOne)
  .delete(controller.remove)
  .put(controller.update);
