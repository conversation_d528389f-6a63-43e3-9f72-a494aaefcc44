import { getFilePath } from '../../utils/fileUtils';
import fs from 'fs';
import path from 'path';
import * as responseAction from '../../utils/responseAction';


var mime = {
  html: 'text/html',
  txt: 'text/plain',
  css: 'text/css',
  gif: 'image/gif',
  jpg: 'image/jpeg',
  png: 'image/png',
  svg: 'image/svg+xml',
  js: 'application/javascript',
};

export function previewFile(req, res) {
  const file = getFilePath(req.params.id);
  const type = mime[path.extname(file).slice(1)] || 'text/plain';
  const s = fs.createReadStream(file);
  s.on('open', function() {
    res.set('Content-Type', type);
    s.pipe(res);
  });
  s.on('error', function() {
    res.set('Content-Type', 'text/plain');
    res.status(404).end('Not found');
  });
}


export function downloadFile(req, res) {
  const filePath = getFilePath(req.params.id);
  if (fs.existsSync(filePath)) {
    return res.download(filePath, req.query.fileName);
  }
  return responseAction.error(res, null, 404, 'messagemessagemessage');
}
