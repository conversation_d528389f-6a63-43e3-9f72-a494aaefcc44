import * as responseAction from '../../../utils/responseAction';
import queryHelper from '../../../helpers/queryHelper';
import * as VattuDauKyService from '../vattudauky/vattudauky.service';
import * as YeuCauService from '../dieuchuyenvattu/yeucauvattu/yeucauvattu.service';
import * as TonKhoService from './tonkho.service';
import * as BangTheoDoiService from './bangtheodoi.service';
import { tonKhoTheoAllDonVi } from './tonkho.service';

export async function getTonKhoDanhDiem(req, res) {
  try {
    const tgDauky = queryHelper.extractQueryParam(req).criteria.tg_dau_ky;
    const tgCuoiky = queryHelper.extractQueryParam(req).criteria.tg_cuoi_ky;
    const idDonvi = queryHelper.extractQueryParam(req).criteria.don_vi_id;
    const idDanhdiem = queryHelper.extractQueryParam(req).criteria.danh_diem_id;
    const tonKhoTheoDanhDiem = await TonKhoService.tonkhoTheoDanhDiem(tgDauky, tgCuoiky, idDonvi, idDanhdiem);
    responseAction.success(res, tonKhoTheoDanhDiem);
  } catch (err) {
    console.log(err);
    responseAction.error(res, err);
  }
}

export async function getTonKhoMaVatTu(req, res) {
  try {
    const tgBatDau = queryHelper.extractQueryParam(req).criteria.tg_dau_ky;
    const tgKetThuc = queryHelper.extractQueryParam(req).criteria.tg_cuoi_ky;
    const idDonvi = queryHelper.extractQueryParam(req).criteria.don_vi_id;
    const idVattu = queryHelper.extractQueryParam(req).criteria.vat_tu_id;
    const idDanhDiem = queryHelper.extractQueryParam(req).criteria.id_danh_diem;
    const serial = queryHelper.extractQueryParam(req).criteria.serial;
    const tonKho = await TonKhoService.tonkhoTheoVattu(tgBatDau, tgKetThuc, idDonvi, idVattu, idDanhDiem, serial);
    responseAction.success(res, tonKho);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function getTonKhoDonVi(req, res) {
  try {
    const tgDauky = queryHelper.extractQueryParam(req).criteria.tg_dau_ky;
    const tgCuoiky = queryHelper.extractQueryParam(req).criteria.tg_cuoi_ky;
    const idDonvi = queryHelper.extractQueryParam(req).criteria.don_vi_id;
    const tonKhoTheoDanhDiem = await TonKhoService.tonkhoTheoDanhDiem(tgDauky, tgCuoiky);
    const tonKhoTheoDonVi = await TonKhoService.tonKhoTheoDonVi(tonKhoTheoDanhDiem, idDonvi);
    responseAction.success(res, tonKhoTheoDonVi);
  } catch (err) {
    console.log(err);
    responseAction.error(res, err);
  }
}
