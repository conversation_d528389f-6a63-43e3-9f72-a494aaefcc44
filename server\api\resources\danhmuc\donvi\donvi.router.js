import express from 'express';
import passport from 'passport';
import * as donviController from './donvi.controller';
import { authorizationMiddleware } from '../../rbac/middleware';
import DanhMucPermission from '../../rbac/permissions/danhmuc/DanhMucPermission';

export const donviRouter = express.Router();

donviRouter.use(passport.authenticate('jwt', { session: false }));
donviRouter.get('*', authorizationMiddleware([DanhMucPermission.READ]));
donviRouter.post('*', authorizationMiddleware([DanhMucPermission.CREATE]));
donviRouter.put('*', authorizationMiddleware([DanhMucPermission.UPDATE]));
donviRouter.delete('*', authorizationMiddleware([DanhMucPermission.DELETE]));
donviRouter
  .route('/')
  .get(donviController.getAll)
  .post(donviController.create);

donviRouter
  .route('/:id')
  .get(donviController.findOne)
  .delete(donviController.remove)
  .put(donviController.update);
