import mongoose, { Schema } from 'mongoose';
import {
  KHOVATTU,
  GIAOVATTU_CANHAN,
  VATTU,
  GIAONHAN_CHITIET,
  TINHTRANG,
  NHANVIEN, DANHDIEMVATTU, TRA_VATTU, TRA_VATTU_CHITIET,
} from '../../../../constant/dbCollections';
import mongoosePaginate from 'mongoose-paginate-v2';

const schema = new Schema({
  id_travattu: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: TRA_VATTU,
  },
  id_vattu:{
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: VATTU,
  },
  id_nhanvien: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: NHANVIEN,
  },
  id_tinhtrang: {
    type: mongoose.Schema.Types.ObjectId,
    // required: false,
    ref: TINHTRANG,
  },
  id_tinhtrang_hientai: {
    type: mongoose.Schema.Types.ObjectId,
    required: false,
    ref: TINHTRANG,
  },
  ghichu: { type: String },
  soluong_giu: { type: Number, required: true },
  soluong_tra: { type: Number, required: true },
  isActive: { type: Boolean, default: true },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  versionKey: false,
});
schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(TRA_VATTU_CHITIET, schema);
