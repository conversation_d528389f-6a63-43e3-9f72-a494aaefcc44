import * as ValidatorHelper from '../../../helpers/validatorHelper';
import THANHLY from './thanhlyvattu.model';
import { createEventQuery, formatDate, groupBy } from '../../../common/functionCommons';
import * as ThanhLyChiTietService from './chitiet/service';

const Joi = require('joi');

const objSchema = Joi.object({
  id_nguoi: Joi.string().required().messages(ValidatorHelper.messageDefine('Người tiến hành')),
  id_donvi: Joi.string().required().messages(ValidatorHelper.messageDefine('Đơn vị')),
  trang_thai: Joi.string().messages(ValidatorHelper.messageDefine('Hoàn thành')),
  thoi_gian_thuc_hien: Joi.date().required().messages(ValidatorHelper.messageDefine('<PERSON><PERSON><PERSON> thanh lý')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}


export async function getAll(query) {
  return THANHLY.find(query).lean();
}

export async function getForLyDoGiam(tgBatDau, tgKetThuc, idDonvi) {
  const queryThanhLy = createEventQuery(tgBatDau, tgKetThuc);
  if (idDonvi) {
    queryThanhLy.id_donvi = idDonvi;
  }
  const thanhLy = (await THANHLY.find(queryThanhLy)).map(item => item._id);
  let allThanhLyChitiet = await ThanhLyChiTietService.getAll({ id_thanhly: { $in: thanhLy } })
    .populate([
      { path: 'id_thanhly' },
      { path: 'id_vattu', populate: { path: 'id_danh_diem', populate: 'ma_don_vi_tinh' } },
    ]);

  allThanhLyChitiet.forEach(item => {
    item.id_danh_diem = item.id_vattu.id_danh_diem._id;
  });
  const groupByDanhDiem = groupBy(allThanhLyChitiet, 'id_danh_diem');

  const mapDanhDiem = {};
  allThanhLyChitiet.forEach(item => {
    if (mapDanhDiem[item.id_danh_diem]) {
      mapDanhDiem[item.id_danh_diem].so_luong += item.soluong;
    } else {
      mapDanhDiem[item.id_danh_diem] = {
        ...{
          so_luong: item.soluong,
          loai: item.id_vattu.id_danh_diem.ten_danh_diem,
          don_vi_tinh: item.id_vattu.id_danh_diem.ma_don_vi_tinh.ten_don_vi_tinh.toLowerCase(),
          ngay_thanh_ly: item.id_thanhly.thoi_gian_hoan_thanh ? formatDate(item.id_thanhly.thoi_gian_hoan_thanh) : '',
        },
      };
    }
  });
  let lyDoGiam = [];
  Object.keys(groupByDanhDiem).forEach(danhdiem => {
    lyDoGiam.push({ ...mapDanhDiem[danhdiem], ...{ stt: '-', type: 'thanh_ly' } });
  });
  return lyDoGiam;
}
