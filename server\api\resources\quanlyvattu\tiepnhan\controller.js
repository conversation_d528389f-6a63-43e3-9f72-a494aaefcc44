import * as responseAction from '../../../utils/responseAction';
import queryHelper from '../../../helpers/queryHelper';
import * as Service from './service';
import Model from './model';
import CommonError from '../../../error/CommonError';
import * as ChiTietService from '../tiepnhan/chitiet/service';
import exelUtils from '../../../utils/exelUtils';


export async function findOne(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findOne({ _id: id, is_deleted: false })
      .populate({ path: 'id_donvi', select: 'ten_don_vi' })
      .lean();
    if (!data) {
      return responseAction.error(res, CommonError.NOT_FOUND());
    }
    data.chitiet = await ChiTietService.getAll({ id_tiepnhan: data._id, is_deleted: false })
      .populate('id_tinhtrang')
      .populate({
        path: 'id_vattu',
        populate: [
          { path: 'id_nguon_cap' },
          { path: 'id_danh_diem', populate: 'ma_don_vi_tinh' },
        ],
      }).lean();
    return responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function remove(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findOneAndUpdate({ _id: id }, { is_deleted: true }, { new: true }).lean();
    if (!data) {
      return responseAction.error(res, CommonError.NOT_FOUND());
    } else {
      await ChiTietService.removeAll({ id_tiepnhan: data._id });
      data.chitiet = await ChiTietService.getAll({ id_tiepnhan: data._id });
    }
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function update(req, res) {
  try {
    const { id } = req.params;
    const { error, value } = Service.validate(req.body);
    if (error) return responseAction.error(res, error, 400);
    const data = await Model.findByIdAndUpdate(id, value, { new: true }).lean();
    if (!data) {
      return responseAction.error(res, CommonError.NOT_FOUND());
    } else {
      const chitiet = req.body.chitiet || [];
      const chitietUpdate = chitiet.filter(row => row._id);
      await ChiTietService.updateAll(chitietUpdate);
      let chitietCreate = chitiet.filter(row => !row.hasOwnProperty('_id'));

      chitietCreate.forEach(row => row.id_tiepnhan = data._id);
      await ChiTietService.create(res, chitietCreate);
      data.chitiet = await ChiTietService.getAll({ id_tiepnhan: data._id, is_deleted: false })
        .populate({
          path: 'id_vattu', populate: [
            { path: 'id_danh_diem', populate: 'ma_don_vi_tinh' },
            { path: 'id_nguon_cap' },
          ],
        })
        .populate('id_tinhtrang');
    }
    responseAction.success(res, data);
  } catch (err) {
    console.log(err);
    responseAction.error(res, err);
  }
}

export async function create(req, res) {
  try {
    const { error, value } = Service.validate(req.body);
    if (error) return responseAction.error(res, error, 400);
    let data = await Model.create(value);
    if (data && req.body.chitiet && Array.isArray(req.body.chitiet)) {
      data = data.toObject();
      req.body.chitiet.forEach((row) => {
        row.id_tiepnhan = data._id;
      });
      data.chitiet = await ChiTietService.create(res, req.body.chitiet);
    }
    return responseAction.success(res, data);
  } catch (err) {
    console.error(err);
    return responseAction.error(res, err, 500);
  }
}

export async function getAll(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria, options } = query;
    options.populate = [
      { path: 'id_donvi', select: 'ten_don_vi' },
    ];
    const data = await Model.paginate(criteria, options);
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

const SHEET_NAMES = {
  VAT_TU_DAU_KY: 'Vật tư đầu kỳ',
  TIEP_NHAN: 'Tiếp nhận',
};

export async function checkImportTiepNhan(req, res) {

  try {
    let filePath = req.files.file.path;
    const sheetData = await exelUtils.transformFile(filePath);
    const resultArray = await checkImportByData(sheetData);
    responseAction.success(res, resultArray);
  } catch (e) {
    console.log(e);
    responseAction.error(res, e);
  }
}

async function checkImportByData(sheetData) {

  function getSheetByName(sheetData, name) {
    return sheetData.find((sheet) => sheet.name.toLowerCase().includes(name.toLowerCase()));
  }

  let resultArray = [];
  resultArray = [...resultArray, await Service.checkImport(getSheetByName(sheetData, SHEET_NAMES.TIEP_NHAN))];
  return resultArray;
}

export async function importTiepNhan(req, res) {
  try {
    const sheetData = req.body;
    const afterImportSuplies = await Service.createSuplies(sheetData);
    const result = await Service.convertData(afterImportSuplies);
    return responseAction.success(res, result);
  } catch (e) {
    console.log(e);
    return responseAction.error(res, e);
  }
}
