import {authorizePermission} from "./authorizationHelper";
import {error} from "../../common/responseHelper";
import Error from './Error'

const defaultPermissionExtractor = (req) => {
  let permissions = []
  const userInfo = req.user
  const roles = userInfo.role_id
  if (roles && roles.length > 0) {
    roles.forEach(role => {
      permissions = [...permissions, ...role.permissions]
    })
  }
  return permissions
}

function authorizationMiddleware(requiredPermissions, permissionExtractor = defaultPermissionExtractor) {
  return (req, res, next) => {
    const permissionGranted = permissionExtractor(req)
    if (permissionGranted && authorizePermission(permissionGranted, requiredPermissions)) {
      next()
    } else {
      error(res, Error.INSUFFICIENT_PERMISSION)
    }
  }
}

export {authorizationMiddleware}
