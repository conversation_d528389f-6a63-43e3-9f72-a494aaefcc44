import ModelNhanVien from '../danhmuc/nhanvien/nhanvien.model';
import ModelDonVi from '../danhmuc/donvi/donvi.model';
import * as responseAction from '../../utils/responseAction';
import * as Service from '../dashboard/service';

export async function getAllNhanVienByDonVi(req, res) {
  try {
    let dataRes = [];
    let staffs = [];
    staffs = [...staffs, ...await ModelNhanVien.find({is_deleted: false})];
    const orgUnit = await ModelDonVi.find({is_deleted: false});
    orgUnit.forEach(org => {
      let countStaff = 0;
      staffs.forEach(staff => {
        if (staff.id_don_vi.toString() === org._id.toString()) {
          countStaff++;
        }
      });
      let data = {
        donVi: org.ten_don_vi,
        soLuongNhanVien: countStaff,
      };
      dataRes = [...dataRes, data];
    });
    responseAction.success(res, dataRes);
  } catch (err) {
    return responseAction.error(res, err, 500);
  }
}

export const getAllEvent = async (req, res) => {
  try {
    const { id } = req.params;
    const allEvents = await Service.getLichSuEventTheoDonVi(id);
    responseAction.success(res, allEvents);
  } catch (err) {
    responseAction.error(res, err);
  }
};

export const getAllEventDetail = async (req, res) => {
  try {
    const { id } = req.params;
    const eventDetails = await Service.getEventDetailsTheoDonVi(id);
    responseAction.success(res, eventDetails);
  } catch (err) {
    responseAction.error(res, err);
  }
};
