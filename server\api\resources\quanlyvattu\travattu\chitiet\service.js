import * as ValidatorHelper from '../../../../helpers/validatorHelper';
import TRAVATTU_CHITIET from './model';

const Joi = require('joi');

export async function create(data) {
  const { error, value } = validate(data);
  if (error) throw error;
  return TRAVATTU_CHITIET.create(value);
}

export async function updateAll(chitietUpdate) {
  for (const row of chitietUpdate) {
    const { error, value } = validate(row);
    if (error) throw error;
    await TRAVATTU_CHITIET.findByIdAndUpdate(value._id, value);
  }
}

export function getAll(query) {
  return TRAVATTU_CHITIET.find(query).lean();
}

export function getAllWithInfo(query) {
  return TRAVATTU_CHITIET.find(query)
    .populate('id_nhanvien')
    .populate('id_vattu')
    .populate({ path: 'id_travattu', populate: 'id_donvi' })
    .lean();
}

export function getForTonKho(query) {
  return TRAVATTU_CHITIET.find(query)
    .populate('id_travattu')
    .lean();
}

export async function removeAll(query) {
  return TRAVATTU_CHITIET.updateMany(query, { is_deleted: true });
}

const objSchema = Joi.object({
  id_travattu: Joi.string().required().messages(ValidatorHelper.messageDefine('Trả vật tư')),
  id_nhanvien: Joi.string().messages(ValidatorHelper.messageDefine('Nhân viên')),
  soluong: Joi.number().required().messages(ValidatorHelper.messageDefine('Số lượng')),
  id_tinhtrang: Joi.number().required().messages(ValidatorHelper.messageDefine('Tình trạng')),
  ghichu: Joi.string().required().messages(ValidatorHelper.messageDefine('Ghi chú')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  if (Array.isArray(data)) {
    let validateError = null;
    data.find(itemData => {
      const { value, error } = schema.validate(itemData, { allowUnknown: true, abortEarly: true });
      if (error) validateError = error;
      return error;
    });
    if (validateError && validateError.details) {
      return { validateError };
    }
    return { value: data };
  } else {
    const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
    if (error && error.details) {
      return { error };
    }
    return { value };
  }
}
