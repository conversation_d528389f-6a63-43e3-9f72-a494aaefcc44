import express from 'express';
import passport from 'passport';
import * as nguoncapController from './nguoncap.controller';
import { authorizationMiddleware } from '../../rbac/middleware';
import DanhMucPermission from '../../rbac/permissions/danhmuc/DanhMucPermission';

export const nguoncapRouter = express.Router();

nguoncapRouter.use(passport.authenticate('jwt', { session: false }));
nguoncapRouter.get('*', authorizationMiddleware([DanhMucPermission.READ]));
nguoncapRouter.post('*', authorizationMiddleware([DanhMucPermission.CREATE]));
nguoncapRouter.put('*', authorizationMiddleware([DanhMucPermission.UPDATE]));
nguoncapRouter.delete('*', authorizationMiddleware([DanhMucPermission.DELETE]));

nguoncapRouter
  .route('/')
  .get(nguoncapController.getAll)
  .post(nguoncapController.create);

nguoncapRouter
  .route('/:id')
  .get(nguoncapController.findOne)
  .delete(nguoncapController.remove)
  .put(nguoncapController.update);
