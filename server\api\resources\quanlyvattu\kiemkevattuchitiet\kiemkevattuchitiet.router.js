import express from 'express';
import passport from 'passport';
import * as vatTuDauKyController from './kiemkevattuchitiet.controller';

export const kiemkevattuchitietRouter = express.Router();
kiemkevattuchitietRouter
  .route('/')
  .get(passport.authenticate('jwt', { session: false }), vatTuDauKyController.getAll)
  .post(passport.authenticate('jwt', { session: false }), vatTuDauKyController.create)

kiemkevattuchitietRouter
  .route('/:id')
  .get(passport.authenticate('jwt', { session: false }), vatTuDauKyController.findOne)
  .delete(passport.authenticate('jwt', { session: false }), vatTuDauKyController.remove)
  .put(passport.authenticate('jwt', { session: false }), vatTuDauKyController.update);
