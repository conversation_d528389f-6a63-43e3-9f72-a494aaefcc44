import express from 'express';
import passport from 'passport';
import * as dieuchuyenvattuController from './dieuchuyenvattu.controller';
import { authorizationMiddleware } from '../../rbac/middleware';
import QuanLyVatTuPermisson from '../../rbac/permissions/quanlyvattu/QuanLyVatTuPermisson';


export const dieuchuyenvattuRouter = express.Router();

dieuchuyenvattuRouter.use(passport.authenticate('jwt', { session: false }));

dieuchuyenvattuRouter.post('*', authorizationMiddleware([QuanLyVatTuPermisson.CREATE]));
dieuchuyenvattuRouter.get('*', authorizationMiddleware([QuanLyVatTuPermisson.READ]));
dieuchuyenvattuRouter.put('/duyet/:id', authorizationMiddleware([QuanLyVatTuPermisson.CONFIRM]));
dieuchuyenvattuRouter.put('/:id', authorizationMiddleware([QuanLyVatTuPermisson.UPDATE]));
dieuchuyenvattuRouter.delete('*', authorizationMiddleware([QuanLyVatTuPermisson.DELETE]));

dieuchuyenvattuRouter
  .route('/')
  .get(dieuchuyenvattuController.getAll)
  .post(dieuchuyenvattuController.create);
dieuchuyenvattuRouter
  .route('/duyet/:id')
  .put(dieuchuyenvattuController.update)

dieuchuyenvattuRouter
  .route('/:id')
  .get(dieuchuyenvattuController.findOne)
  .delete(dieuchuyenvattuController.remove)
  .put(dieuchuyenvattuController.update);
