import mongoose, { Schema } from 'mongoose';
import {
  D<PERSON><PERSON>,
  NHANVI<PERSON>,
  GIAOVATTU_CANHAN,
  VATTU,
  DANHDIEMVATTU,
  TOCHUC_QUANLY,
} from '../../../constant/dbCollections';
import mongoosePaginate from 'mongoose-paginate-v2';
import { TRANG_THAI } from '../../../constant/constant';

const schema = new Schema({
  id_donvi: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: TOCHUC_QUANLY,
  },
  thoi_gian_ban_giao_ca_nhan: { type: Date},
  trang_thai: {
    type: String,
    enum: Object.values(TRANG_THAI),
    default: TRANG_THAI.DANG_THUC_HIEN,
  },
  thoi_gian_hoan_thanh: { type: Date, required: false },
  isActive: { type: Boolean, default: true },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  versionKey: false,
});
schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(GIAOVATTU_CANHAN, schema);
