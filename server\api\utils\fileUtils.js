import fs from 'fs';
import { FILE_MISSING } from '../constant/messageError';
import path from 'path';
import { getConfig } from '../../config/config';
import multipart from 'connect-multiparty';

const config = getConfig(process.env.NODE_ENV);

export function deleteFile(filePath) {
  fs.unlink(filePath, () => {
  });
}

const osTempDir = require('os').tmpdir();
const tempDir = path.join(osTempDir, 'uploads');
const filesDir = path.resolve('./storage');
export const getDirPath = (dirName, rootPath = './storage') => {
  const dirPath = path.resolve(rootPath, dirName);
  createFolderIfNotExist(dirPath);
  return dirPath;
};

function createFolderIfNotExist(folderPath) {
  if (!fs.existsSync(folderPath)) {
    fs.mkdirSync(folderPath);
  }
}

const checkTempFolder = (req, res, next) => {
  createFolderIfNotExist(tempDir);
  createFolderIfNotExist(filesDir);
  const filesTemplatesDir = getDirPath('templates');
  createFolderIfNotExist(filesTemplatesDir);
  next();
};

const prepareTempFolder = () => {
  createFolderIfNotExist(tempDir);
  createFolderIfNotExist(filesDir);
  const filesTemplatesDir = getDirPath('templates');
  createFolderIfNotExist(filesTemplatesDir);
  clearFolder(tempDir);
};

const clearFolder = (tempDir) => {
  fs.readdir(tempDir, (err, files) => {
    if (err) {
      console.log(err);
      return;
    }
    for (const file of files) {
      fs.unlink(path.join(tempDir, file), err => {
        if (err) {
          console.log(file, err);
        }
      });
    }
  });
};

prepareTempFolder();

const getFileExtension = (filename) => {
  let ext = /^.+\.([^.]+)$/.exec(filename);
  return ext === null ? '' : ext[1];
};

const createByName = (filePath, fileName) => {
  return new Promise((resolve, reject) => {
    let file = fs.createReadStream(filePath);
    file.on('error', (err) => {
      console.log(err);
      deleteFile(filePath);
      reject(FILE_MISSING);
    });
    copyFileToStorage(filePath, fileName).then((newFilePath) => {
      deleteFile(filePath);
      resolve(newFilePath);
    }).catch(err => {
      console.log('Bucket is not exists or you dont have permission to access it.');
      console.log(err);
      deleteFile(filePath);
      reject(err);
    });
  });
};

const remove = (fileName) => {
  return new Promise((resolve, reject) => {
    try {
      deleteFile(getFilePath(fileName));
      resolve(fileName);
    } catch (e) {
      reject(e);
    }
  });
};
const getUrlFile = (fileName) => {
  return `${config.backend_base_url}/api/v1/image/${fileName}`;
};

export const getFilePath = (fileName, filesDir = './storage') => {
  return path.join(filesDir, fileName);
};

export const getFilePipe = (fileName) => {
  const filePath = getFilePath(fileName);
  return fs.createReadStream(filePath);
};

function createUniqueFileName(filePath) {
  let fileName;
  if (filePath) {
    let fileExtension = getFileExtension(filePath);
    console.log(filePath);
    let name = path.parse(filePath).name;
    let timeStamp = (new Date()).toISOString();
    timeStamp = timeStamp.replace(/:/g, '-');
    fileName = fileExtension === '' ? `${name}_${timeStamp}` : `${name}_${timeStamp}.${fileExtension}`;
  }
  return fileName;
}

export function getName(filePath) {
  return path.parse(filePath).base;
}

const downloadFile = (async (url, path) => {
  const res = await fetch(url);
  const fileStream = fs.createWriteStream(path);
  await new Promise((resolve, reject) => {
    res.body.pipe(fileStream);
    res.body.on('error', (err) => {
      reject(err);
    });
    fileStream.on('finish', function() {
      resolve();
    });
  });
});

async function copyFileToStorage(srcPath, fileName) {
  const desPath = `${filesDir}/${fileName}`;
  return copyFile(srcPath, desPath);
}

async function copyFileToFolder(srcPath, desPath, fileName) {
  return copyFile(srcPath, desPath);
}

async function copyFile(srcPath, desPath) {
  return new Promise((resolve, reject) => {
    fs.copyFile(srcPath, desPath, (err) => {
      if (err) reject(err);
      resolve(desPath);
    });
  });
}

export const multipartMiddleware = multipart({ uploadDir: tempDir });

export {
  createByName,
  remove,
  getUrlFile,
  getFileExtension,
  prepareTempFolder,
  createUniqueFileName,
  checkTempFolder,
  downloadFile,
  tempDir,
};
