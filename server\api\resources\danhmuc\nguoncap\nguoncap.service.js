import * as ValidatorHelper from '../../../helpers/validatorHelper';
import NGUON_CAP from './nguoncap.model';

const Joi = require('joi');

export async function getAll(query) {
  return NGUON_CAP.find(query).lean();
}

const objSchema = Joi.object({
  id_nguon_cap: Joi.string().required().messages(ValidatorHelper.messageDefine('Mã nguồn cấp')),
  ten_nguon_cap: Joi.string().required().messages(ValidatorHelper.messageDefine('Tên nguồn cấp')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}
