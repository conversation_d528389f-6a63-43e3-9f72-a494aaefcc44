import * as VattuDauKyService from '../vattudauky/vattudauky.service';
import * as DieuchuyenService from '../dieuchuyenvattu/dieuchuyenvattu.service';
import * as DieuchuyenChiTietService from '../dieuchuyenvattu/dieuchuyenvattuchitiet/dieuchuyenvattuchitiet.service';
import * as YeuCauChiTietService from '../dieuchuyenvattu/yeucauvattu/yeucauvattuchitiet/yeucauvattuchitiet.service';
import * as TiepNhanService from '../tiepnhan/service';
import * as TiepNhanChiTietService from '../tiepnhan/chitiet/service';
import * as ThanhLyService from '../thanhlyvattu/thanhlyvattu.service';
import * as ThanhLyChiTietService from '../thanhlyvattu/chitiet/service';
import * as TieuHuyService from '../tieuhuy/service';
import * as TieuHuyChiTietService from '../tieuhuy/chitiet/service';
import * as BanGiaoCaNhanService from '../bangiaocanhan/service';
import * as BanGiaoCaNhanChiTietService from '../bangiaocanhan/chitiet/service';
import * as TraVatTuService from '../travattu/service';
import * as TraVatTuChiTietService from '../travattu/chitiet/service';
import * as CapNhatTinhTrangService from '../capnhattinhtrangvattu/capnhattinhtrangvattu.service';
import * as CapNhatTinhTrangChiTietService from '../capnhattinhtrangvattu/chitiet/service';
import { TRANG_THAI } from '../../../constant/constant';
import { createEventQuery } from '../../../common/functionCommons';
import Joi from 'joi';
import {
  CAPNHATTINHTRANG,
  GIAOVATTU_CANHAN,
  THANHLY,
  TIEP_NHAN,
  TIEUHUY,
  TRA_VATTU,
  VATTU_DAUKY,
} from '../../../constant/dbCollections';
import * as VatTuService from '../../danhmuc/vattu/vattu.service';
import VATTU from '../../danhmuc/vattu/vattu.model';

function createGroupKey(...keys) {
  return keys.join();
}

const rowBangTheoDoiSchema = Joi.object({
  id_don_vi: Joi.string().required(),
  id_vattu: Joi.string().required(),
  id_tinh_trang: Joi.string().required(),
  tonkho_dauky: Joi.number().required(),
  soluong_tang: Joi.number().required(),
  soluong_tang_dukien: Joi.number().required(),
  soluong_giam: Joi.number().required(),
  soluong_giam_dukien: Joi.number().required(),
  soluong_trongkho_tang: Joi.number().required(),
  soluong_trongkho_giam: Joi.number().required(),
  soluong_trongkho_thucte: Joi.number().required(),
  type: Joi.string().required(),
  thoi_gian_hoan_thanh: Joi.date().required(),
});

const rowBangTonKhoSchema = Joi.object({
  id_don_vi: Joi.string().required(),
  id_vattu: Joi.string().required(),
  id_tinh_trang: Joi.string().required(),
  thoigian_dauky: Joi.date().required(),
  tonkho_dauky: Joi.number().required(),
  soluong_tang: Joi.number().required(),
  soluong_tang_dukien: Joi.number().required(),
  soluong_giam: Joi.number().required(),
  soluong_giam_dukien: Joi.number().required(),
  soluong_trongkho_tang: Joi.number().required(),
  soluong_trongkho_giam: Joi.number().required(),
  soluong_trongkho_thucte: Joi.number().required(),
  tonkho_cuoiky: Joi.number().required(),
  tonkho_cuoiky_dukien: Joi.number().required(),
  thoigian_cuoiky: Joi.date().required(),
  type: Joi.string().required(),
  thoi_gian_hoan_thanh: Joi.date().required(),
});

const rowBangTonKhoDanhDiemSchema = Joi.object({
  id_don_vi: Joi.string().required(),
  id_danhdiem: Joi.string().required(),
  id_tinh_trang: Joi.string().required(),
  thoigian_dauky: Joi.date().required(),
  tonkho_dauky: Joi.number().required(),
  soluong_tang: Joi.number().required(),
  soluong_tang_dukien: Joi.number().required(),
  soluong_giam: Joi.number().required(),
  soluong_giam_dukien: Joi.number().required(),
  tonkho_cuoiky: Joi.number().required(),
  tonkho_cuoiky_dukien: Joi.number().required(),
  thoigian_cuoiky: Joi.date().required(),
  type: Joi.string().required(),
  thoi_gian_hoan_thanh: Joi.date().required(),
});


export async function createTheoDoiRowsFromDauKy(tgDauky, tgCuoiky, idDonvi, vattuIds) {
  async function getAllVattuDauKy(idDonvi, tgDauky, tgCuoiky, vattuIds) {
    // lấy tất cả vattudauky có trong khoảng thời gian tgDauky và tgCuoiky của đơn vị và kho theo query
    const query = {};
    query.is_deleted = false;
    if (tgCuoiky || tgDauky) {
      query.thoigian_dauky = {};
      if (tgCuoiky) {
        query.thoigian_dauky.$lt = new Date(tgCuoiky);
      }
      if (tgDauky) {
        query.thoigian_dauky.$gte = new Date(tgDauky);
      }
    }
    if (vattuIds) {
      query.id_vattu = { $in: vattuIds };
    }
    if (idDonvi) {
      query.id_don_vi = idDonvi;
    }
    return await VattuDauKyService.getForTonKhoNew(query);
  }

  const allVattuDauKy = await getAllVattuDauKy(idDonvi, tgDauky, tgCuoiky, vattuIds);

  function convertDauKyRowsToTheoDoiRows(vattudaukymoinhat) {
    return vattudaukymoinhat.map(element => {
      return {
        id_don_vi: element.id_don_vi,
        id_vattu: element.id_vattu,
        soluong_giam: 0,
        soluong_tang: 0,
        soluong_giam_dukien: 0,
        soluong_tang_dukien: 0,
        tonkho_dauky: element.so_luong,
        id_tinh_trang: element.id_tinh_trang,
        thoi_gian_hoan_thanh: element.thoigian_dauky,
        type: VATTU_DAUKY,
      };
    });
  }

  return convertDauKyRowsToTheoDoiRows(allVattuDauKy);
}

export async function createTheoDoiRowsFromDieuChuyen(tgDauky, tgCuoiky, idDonvi, vattuIds) {
  async function getAllDieuChuyen(idDonvi, tgDauky, tgCuoiky, vattuIds, trangThai = [TRANG_THAI.THUC_HIEN_THANH_CONG]) {
    const queryDieuChuyen = createEventQuery(tgDauky, tgCuoiky, trangThai);
    if (idDonvi) {
      queryDieuChuyen.$or = [
        { id_donvi_di: idDonvi },
        { id_donvi_den: idDonvi },
      ];
    }
    const dieuchuyen = (await DieuchuyenService.getAll(queryDieuChuyen)).map(dc => dc._id);
    const queryDieuChuyenChiTiet = {};
    queryDieuChuyenChiTiet.id_dieu_chuyen = { $in: dieuchuyen };
    if (vattuIds) {
      queryDieuChuyenChiTiet.id_vattu = { $in: vattuIds };
    }
    return DieuchuyenChiTietService.getForTonKho(queryDieuChuyenChiTiet);
  }

  const allDieuchuyen = await getAllDieuChuyen(idDonvi, tgDauky, tgCuoiky, vattuIds);
  const allDieuchuyenDuKien = await getAllDieuChuyen(idDonvi, tgDauky, tgCuoiky, vattuIds, [TRANG_THAI.DANG_THUC_HIEN]);

  const allEventData = [...allDieuchuyenDuKien, ...allDieuchuyen];

  function convertDieuChuyenRowsToTheoDoiRows(chitietDieuchuyen) {
    let bangTheoDoi = [];
    chitietDieuchuyen.forEach(element => {
      const dieuchuyen = element.id_dieu_chuyen;
      const thongtin = {
        id_vattu: element.id_vattu,
        id_tinh_trang: element.id_tinhtrang,
        tonkho_dauky: 0,
        soluong_giam: 0,
        soluong_tang: 0,
        soluong_giam_dukien: 0,
        soluong_tang_dukien: 0,
        thoi_gian_hoan_thanh: dieuchuyen.thoi_gian_hoan_thanh || dieuchuyen.thoi_gian_thuc_hien,
        type: dieuchuyen.phan_loai,
      };
      if (dieuchuyen.trang_thai === TRANG_THAI.THUC_HIEN_THANH_CONG) {
        bangTheoDoi = [...bangTheoDoi, {
          ...thongtin,
          id_don_vi: dieuchuyen.id_donvi_di,
          soluong_giam: element.soluong,
        }];
        bangTheoDoi = [...bangTheoDoi, {
          ...thongtin,
          id_don_vi: dieuchuyen.id_donvi_den,
          soluong_tang: element.soluong,
        }];
      }

      if (dieuchuyen.trang_thai === TRANG_THAI.DANG_THUC_HIEN) {
        bangTheoDoi = [...bangTheoDoi, {
          ...thongtin,
          id_don_vi: dieuchuyen.id_donvi_di,
          soluong_giam_dukien: element.soluong,
        }];
        bangTheoDoi = [...bangTheoDoi, {
          ...thongtin,
          id_don_vi: dieuchuyen.id_donvi_den,
          soluong_tang_dukien: element.soluong,
        }];
      }
    });
    return bangTheoDoi;
  }

  return convertDieuChuyenRowsToTheoDoiRows(allEventData);
}

export async function createTheoDoiRowsFromTiepNhan(tgDauky, tgCuoiky, idDonvi, vattuIds) {
  async function getAllTiepNhan(idDonvi, tgDauky, tgCuoiky, vattuIds) {
    let queryEvent = createEventQuery(tgDauky, tgCuoiky);
    if (idDonvi) {
      queryEvent.id_donvi = idDonvi;
    }
    const eventIds = (await TiepNhanService.getAll(queryEvent)).map(dc => dc._id);
    const queryChiTietEvent = {};
    queryChiTietEvent.id_tiepnhan = { $in: eventIds };
    queryChiTietEvent.is_deleted = false;
    if (vattuIds) {
      queryChiTietEvent.id_vattu = { $in: vattuIds };
    }
    return TiepNhanChiTietService.getForTonKho(queryChiTietEvent);
  }

  const allTiepNhanChiTiet = await getAllTiepNhan(idDonvi, tgDauky, tgCuoiky, vattuIds);

  function convertTiepNhanRowsToTheoDoiRows(allEventDetail) {
    let bangTheoDoi = [];

    allEventDetail.forEach(element => {
      const event = element.id_tiepnhan;
      const thongtin = {
        id_vattu: element.id_vattu,
        id_tinh_trang: element.id_tinhtrang,
        tonkho_dauky: 0,
        soluong_giam: 0,
        soluong_tang: 0,
        soluong_giam_dukien: 0,
        soluong_tang_dukien: 0,
        thoi_gian_hoan_thanh: event.thoi_gian_hoan_thanh || event.thoi_gian_thuc_hien,
        type: TIEP_NHAN,
      };
      if (event.trang_thai === TRANG_THAI.THUC_HIEN_THANH_CONG) {
        bangTheoDoi = [...bangTheoDoi, {
          ...thongtin,
          id_don_vi: event.id_donvi,
          soluong_giam: 0,
          soluong_tang: element.soluong,
          soluong_trongkho_giam: 0,
          soluong_trongkho_tang: 0,
        }];
      }
    });
    return bangTheoDoi;
  }

  return convertTiepNhanRowsToTheoDoiRows(allTiepNhanChiTiet);
}

export async function createTheoDoiRowsFromThanhLy(tgDauky, tgCuoiky, idDonvi, vattuIds) {
  async function getAllThanhLy(idDonvi, tgDauky, tgCuoiky, vattuIds) {
    let queryEvent = createEventQuery(tgDauky, tgCuoiky);
    if (idDonvi) {
      queryEvent.id_donvi = idDonvi;
    }
    const eventIds = (await ThanhLyService.getAll(queryEvent)).map(dc => dc._id);
    const queryChiTietEvent = {};
    queryChiTietEvent.id_thanhly = { $in: eventIds };
    queryChiTietEvent.is_deleted = false;
    if (vattuIds) {
      queryChiTietEvent.id_vattu = { $in: vattuIds };
    }
    return ThanhLyChiTietService.getForTonKho(queryChiTietEvent);
  }

  const allThanhLyChiTiet = await getAllThanhLy(idDonvi, tgDauky, tgCuoiky, vattuIds);

  function convertThanhLyRowsToTheoDoiRows(allEventDetail) {
    let bangTheoDoi = [];
    allEventDetail.forEach(element => {
      const event = element.id_thanhly;
      const thongtin = {
        id_vattu: element.id_vattu,
        id_tinh_trang: element.id_tinhtrang,
        id_don_vi: event.id_donvi,
        tonkho_dauky: 0,
        soluong_giam: 0,
        soluong_tang: 0,
        soluong_giam_dukien: 0,
        soluong_tang_dukien: 0,
        thoi_gian_hoan_thanh: event.thoi_gian_hoan_thanh || event.thoi_gian_thuc_hien,
        type: THANHLY,
      };
      if (event.trang_thai === TRANG_THAI.THUC_HIEN_THANH_CONG) {
        bangTheoDoi = [...bangTheoDoi, {
          ...thongtin,
          soluong_giam: element.soluong,
          soluong_tang: 0,
          soluong_trongkho_giam: 0,
          soluong_trongkho_tang: 0,
        }];
      }
    });
    return bangTheoDoi;
  }

  return convertThanhLyRowsToTheoDoiRows(allThanhLyChiTiet);
}

export async function createTheoDoiRowsFromTieuHuy(tgDauky, tgCuoiky, idDonvi, vattuIds) {
  async function getAllTieuHuy(idDonvi, tgDauky, tgCuoiky, vattuIds) {
    let queryEvent = createEventQuery(tgDauky, tgCuoiky);
    if (idDonvi) {
      queryEvent.id_donvi = idDonvi;
    }
    const eventIds = (await TieuHuyService.getAll(queryEvent)).map(dc => dc._id);
    const queryChiTietEvent = {};
    queryChiTietEvent.id_tieuhuy = { $in: eventIds };
    queryChiTietEvent.is_deleted = false;
    if (vattuIds) {
      queryChiTietEvent.id_vattu = { $in: vattuIds };
    }
    return TieuHuyChiTietService.getForTonKho(queryChiTietEvent);
  }

  const allTieuHuyChiTiet = await getAllTieuHuy(idDonvi, tgDauky, tgCuoiky, vattuIds);

  function convertTieuHuyRowsToTheoDoiRows(allEventDetail) {
    let bangTheoDoi = [];
    allEventDetail.forEach(element => {
      const event = element.id_tieuhuy;
      const thongtin = {
        id_vattu: element.id_vattu,
        id_tinh_trang: element.id_tinhtrang,
        tonkho_dauky: 0,
        soluong_giam: 0,
        soluong_tang: 0,
        soluong_giam_dukien: 0,
        soluong_tang_dukien: 0,
        thoi_gian_hoan_thanh: event.thoi_gian_hoan_thanh || event.thoi_gian_thuc_hien,
        type: TIEUHUY,
      };
      if (event.trang_thai === TRANG_THAI.THUC_HIEN_THANH_CONG) {
        bangTheoDoi = [...bangTheoDoi, {
          ...thongtin,
          id_don_vi: event.id_donvi,
          soluong_giam: element.soluong,
          soluong_tang: 0,
          soluong_trongkho_giam: 0,
          soluong_trongkho_tang: 0,
        }];
      }
    });
    return bangTheoDoi;
  }

  return convertTieuHuyRowsToTheoDoiRows(allTieuHuyChiTiet);
}

export async function createTheoDoiRowsFromBanGiaoCaNhan(tgDauky, tgCuoiky, idDonvi, vattuIds) {
  async function getAllBanGiaoCaNhan(idDonvi, tgDauky, tgCuoiky, vattuIds) {
    let queryEvent = createEventQuery(tgDauky, tgCuoiky);
    delete queryEvent.trang_thai;
    if (idDonvi) {
      queryEvent.id_donvi = idDonvi;
    }
    const eventIds = (await BanGiaoCaNhanService.getAll(queryEvent)).map(dc => dc._id);
    const queryChiTietEvent = {};
    queryChiTietEvent.id_giaonhan = { $in: eventIds };
    queryChiTietEvent.is_deleted = false;
    if (vattuIds) {
      queryChiTietEvent.id_vattu = { $in: vattuIds };
    }
    return BanGiaoCaNhanChiTietService.getForTonKho(queryChiTietEvent);
  }

  const allBanGiaoCaNhanChiTiet = await getAllBanGiaoCaNhan(idDonvi, tgDauky, tgCuoiky, vattuIds);

  function convertBanGiaoCaNhanRowsToTheoDoiRows(allEventDetail) {
    let bangTheoDoi = [];

    allEventDetail.forEach(element => {
      const event = element.id_giaonhan;
      const thongtin = {
        id_vattu: element.id_vattu,
        id_tinh_trang: element.id_tinhtrang,
        tonkho_dauky: 0,
        soluong_trongkho_giam: 0,
        soluong_trongkho_tang: 0,
        soluong_trongkho_thucte: 0,
        thoi_gian_hoan_thanh: event.thoi_gian_hoan_thanh || event.thoi_gian_ban_giao_ca_nhan,
        type: GIAOVATTU_CANHAN,
      };
      if (event.trang_thai === TRANG_THAI.THUC_HIEN_THANH_CONG) {
        bangTheoDoi = [...bangTheoDoi, {
          ...thongtin,
          id_don_vi: event.id_donvi,
          soluong_trongkho_giam: element.soluong,
          soluong_trongkho_tang: 0,
          soluong_giam: 0,
          soluong_tang: 0,
        }];
      }
    });
    return bangTheoDoi;
  }

  return convertBanGiaoCaNhanRowsToTheoDoiRows(allBanGiaoCaNhanChiTiet);
}

export async function createTheoDoiRowsFromTraVatTu(tgDauky, tgCuoiky, idDonvi, vattuIds) {
  async function getAllTraVatTu(idDonvi, tgDauky, tgCuoiky, vattuIds) {
    let queryEvent = createEventQuery(tgDauky, tgCuoiky);
    delete queryEvent.trang_thai;
    if (idDonvi) {
      queryEvent.id_donvi = idDonvi;
    }

    const eventIds = (await TraVatTuService.getAll(queryEvent)).map(dc => dc._id);
    const queryChiTietEvent = {};
    queryChiTietEvent.id_travattu = { $in: eventIds };
    queryChiTietEvent.is_deleted = false;
    if (vattuIds) {
      queryChiTietEvent.id_vattu = { $in: vattuIds };
    }
    return TraVatTuChiTietService.getForTonKho(queryChiTietEvent);
  }

  const allTraVatTuChiTiet = await getAllTraVatTu(idDonvi, tgDauky, tgCuoiky, vattuIds);

  function convertTraVatTuRowsToTheoDoiRows(allEventDetail) {
    let bangTheoDoi = [];

    allEventDetail.forEach(element => {
      const event = element.id_travattu;
      const thongtin = {
        id_vattu: element.id_vattu,
        id_tinh_trang: element.id_tinhtrang,
        id_don_vi: event.id_donvi,
        tonkho_dauky: 0,
        soluong_trongkho_giam: 0,
        soluong_trongkho_tang: 0,
        soluong_trongkho_thucte: 0,
        thoi_gian_hoan_thanh: event.thoi_gian_hoan_thanh || event.thoi_gian_tra_vat_tu,
        type: TRA_VATTU,
      };
      const thongtin_hientai = {
        id_vattu: element.id_vattu,
        id_tinh_trang: element.id_tinhtrang_hientai,
        id_don_vi: event.id_donvi,
        tonkho_dauky: 0,
        soluong_trongkho_giam: 0,
        soluong_trongkho_tang: 0,
        soluong_trongkho_thucte: 0,
        thoi_gian_hoan_thanh: event.thoi_gian_hoan_thanh || event.thoi_gian_tra_vat_tu,
        type: TRA_VATTU,
      };

      if (event.trang_thai === TRANG_THAI.THUC_HIEN_THANH_CONG) {
        bangTheoDoi = [...bangTheoDoi, {
          ...thongtin,
          soluong_giam: 0,
          soluong_tang: 0,
          soluong_trongkho_giam: 0,
          soluong_trongkho_tang: element.soluong_tra,
          soluong_tinhtrang_tang: 0,
          soluong_tinhtrang_giam: element.soluong_tra,
        }];
        bangTheoDoi = [...bangTheoDoi, {
          ...thongtin_hientai,
          soluong_giam: 0,
          soluong_tang: 0,
          soluong_trongkho_giam: 0,
          soluong_trongkho_tang: 0,
          soluong_tinhtrang_tang: element.soluong_tra,
          soluong_tinhtrang_giam: 0,
        }];
      }
    });
    return bangTheoDoi;
  }

  return convertTraVatTuRowsToTheoDoiRows(allTraVatTuChiTiet);
}

export async function createTheoDoiRowsFromCapNhatTinhTrang(tgDauky, tgCuoiky, idDonvi, vattuIds) {
  async function getAllCapNhatTinhTrang(idDonvi, tgDauky, tgCuoiky, vattuIds) {
    const queryEvent = createEventQuery(tgDauky, tgCuoiky);
    if (idDonvi) {
      queryEvent.id_donvi = idDonvi;
    }
    const eventIds = (await CapNhatTinhTrangService.getAll(queryEvent)).map(dc => dc._id);
    const queryChiTietEvent = {};
    queryChiTietEvent.id_capnhat_tinhtrang = { $in: eventIds };
    queryChiTietEvent.is_deleted = false;
    if (vattuIds) {
      queryChiTietEvent.id_vattu = { $in: vattuIds };
    }
    return CapNhatTinhTrangChiTietService.getForTonKho(queryChiTietEvent);
  }

  const allCapNhatTinhTrangChiTiet = await getAllCapNhatTinhTrang(idDonvi, tgDauky, tgCuoiky, vattuIds);

  function convertCapNhatTinhTrangRowsToTheoDoiRows(allEventDetail) {
    let bangTheoDoi = [];
    allEventDetail.forEach(element => {
      const event = element.id_capnhat_tinhtrang;
      const thongtin = {
        id_vattu: element.id_vattu,
        id_tinh_trang: element.id_tinhtrang,
        id_don_vi: event.id_donvi,
        tonkho_dauky: 0,
        soluong_giam: 0,
        soluong_tang: 0,
        soluong_giam_dukien: 0,
        soluong_tang_dukien: 0,
        thoi_gian_hoan_thanh: event.thoi_gian_hoan_thanh || event.thoi_gian_thuc_hien,
        type: CAPNHATTINHTRANG,
      };
      const thongtin_hientai = {
        id_vattu: element.id_vattu,
        id_tinh_trang: element.id_tinhtrang_hientai,
        id_don_vi: event.id_donvi,
        tonkho_dauky: 0,
        soluong_giam: 0,
        soluong_tang: 0,
        soluong_giam_dukien: 0,
        soluong_tang_dukien: 0,
        thoi_gian_hoan_thanh: event.thoi_gian_hoan_thanh || event.thoi_gian_thuc_hien,
        type: CAPNHATTINHTRANG,
      };
      if (event.trang_thai === TRANG_THAI.THUC_HIEN_THANH_CONG) {
        bangTheoDoi = [...bangTheoDoi, {
          ...thongtin,
          soluong_giam: 0,
          soluong_tang: 0,
          soluong_trongkho_giam: 0,
          soluong_trongkho_tang: 0,
          soluong_tinhtrang_tang: 0,
          soluong_tinhtrang_giam: element.soluong,
        }];
        bangTheoDoi = [...bangTheoDoi, {
          ...thongtin_hientai,
          soluong_giam: 0,
          soluong_tang: 0,
          soluong_trongkho_giam: 0,
          soluong_trongkho_tang: 0,
          soluong_tinhtrang_tang: element.soluong,
          soluong_tinhtrang_giam: 0,
        }];
      }
    });
    return bangTheoDoi;
  }

  return convertCapNhatTinhTrangRowsToTheoDoiRows(allCapNhatTinhTrangChiTiet);
}

export async function tonghopBangTheoDoi(tgDauky, tgCuoiky, idDonvi, vattuIds) {

  let bangTheoDoi = [];
  bangTheoDoi = [...bangTheoDoi, ...await createTheoDoiRowsFromDauKy(tgDauky, tgCuoiky, idDonvi, vattuIds)];
  bangTheoDoi = [...bangTheoDoi, ...await createTheoDoiRowsFromDieuChuyen(tgDauky, tgCuoiky, idDonvi, vattuIds)];
  bangTheoDoi = [...bangTheoDoi, ...await createTheoDoiRowsFromThanhLy(tgDauky, tgCuoiky, idDonvi, vattuIds)];
  bangTheoDoi = [...bangTheoDoi, ...await createTheoDoiRowsFromTiepNhan(tgDauky, tgCuoiky, idDonvi, vattuIds)];
  bangTheoDoi = [...bangTheoDoi, ...await createTheoDoiRowsFromTieuHuy(tgDauky, tgCuoiky, idDonvi, vattuIds)];
  bangTheoDoi = [...bangTheoDoi, ...await createTheoDoiRowsFromBanGiaoCaNhan(tgDauky, tgCuoiky, idDonvi, vattuIds)];
  bangTheoDoi = [...bangTheoDoi, ...await createTheoDoiRowsFromTraVatTu(tgDauky, tgCuoiky, idDonvi, vattuIds)];
  bangTheoDoi = [...bangTheoDoi, ...await createTheoDoiRowsFromCapNhatTinhTrang(tgDauky, tgCuoiky, idDonvi, vattuIds)];
  return bangTheoDoi;
}

export async function bangTongHopCuoiKy(tgKetThuc, idDonvi, vattuIds) {
  const tgBatDau = new Date(0);
  let bangTheoDoi = [];
  // Nếu có tgDauky cần tính tồn kho đầu kỳ trước sau đó dùng dữ liệu đấy để tính toán tiếp
  bangTheoDoi = await tonghopBangTheoDoi(tgBatDau, tgKetThuc, idDonvi, vattuIds);
  // Sắp xếp các sự kiện theo thứ tự từ gần đến xa
  bangTheoDoi.sort((a, b) => b.thoi_gian_hoan_thanh - a.thoi_gian_hoan_thanh);
  const tonKho = {};
  bangTheoDoi.forEach(row => {
    const groupKey = createGroupKey(row.id_don_vi, row.id_vattu, row.id_tinh_trang);
    // Khi chưa có bản ghi nào thì tạo 1 bản ghi không số liệu
    if (!tonKho[groupKey]) {
      tonKho[groupKey] = {};
      tonKho[groupKey].id_don_vi = row.id_don_vi;
      tonKho[groupKey].id_vattu = row.id_vattu;
      tonKho[groupKey].id_tinh_trang = row.id_tinh_trang;
      tonKho[groupKey].thoigian_dauky = tgBatDau;
      tonKho[groupKey].tonkho_dauky = 0;
      tonKho[groupKey].soluong_tang = 0;
      tonKho[groupKey].soluong_tang_dukien = 0;
      tonKho[groupKey].soluong_giam = 0;
      tonKho[groupKey].soluong_giam_dukien = 0;
      tonKho[groupKey].tonkho_cuoiky = 0;
      tonKho[groupKey].soluong_trongkho_tang = 0;
      tonKho[groupKey].soluong_trongkho_giam = 0;
      tonKho[groupKey].soluong_tinhtrang_tang = 0;
      tonKho[groupKey].soluong_tinhtrang_giam = 0;
      tonKho[groupKey].soluong_trongkho_thucte = 0;
      tonKho[groupKey].tonkho_cuoiky_dukien = 0;
      tonKho[groupKey].thoi_gian_hoan_thanh = row.thoi_gian_hoan_thanh; // Lấy sự kiện gần nhất làm thời gian hoàn thành
      tonKho[groupKey].thoigian_cuoiky = tgKetThuc; // Lấy thời gian cuối kỳ trong query làm thời gian cuối kỳ
    }
    // Nếu sự kiện diễn ra sau thời gian đầu kỳ thì lấy giá trị
    if (row.thoi_gian_hoan_thanh >= tonKho[groupKey].thoigian_dauky) {
      tonKho[groupKey].tonkho_dauky = tonKho[groupKey].tonkho_dauky + row.tonkho_dauky;
      tonKho[groupKey].soluong_tang = tonKho[groupKey].soluong_tang + row.soluong_tang;
      tonKho[groupKey].soluong_tang_dukien = tonKho[groupKey].soluong_tang_dukien + (row.soluong_tang_dukien || 0);
      tonKho[groupKey].soluong_giam = tonKho[groupKey].soluong_giam + row.soluong_giam;
      tonKho[groupKey].soluong_trongkho_tang = tonKho[groupKey].soluong_trongkho_tang + (row.soluong_trongkho_tang || 0);
      tonKho[groupKey].soluong_trongkho_giam = tonKho[groupKey].soluong_trongkho_giam + (row.soluong_trongkho_giam || 0);
      tonKho[groupKey].soluong_tinhtrang_tang = tonKho[groupKey].soluong_tinhtrang_tang + (row.soluong_tinhtrang_tang || 0);
      tonKho[groupKey].soluong_tinhtrang_giam = tonKho[groupKey].soluong_tinhtrang_giam + (row.soluong_tinhtrang_giam || 0);
      tonKho[groupKey].soluong_giam_dukien = tonKho[groupKey].soluong_giam_dukien + (row.soluong_giam_dukien || 0);
      tonKho[groupKey].tonkho_cuoiky = tonKho[groupKey].tonkho_dauky + tonKho[groupKey].soluong_tang - tonKho[groupKey].soluong_giam + tonKho[groupKey].soluong_tinhtrang_tang - tonKho[groupKey].soluong_tinhtrang_giam;
      tonKho[groupKey].soluong_trongkho_thucte = tonKho[groupKey].tonkho_cuoiky + tonKho[groupKey].soluong_trongkho_tang - tonKho[groupKey].soluong_trongkho_giam;
      tonKho[groupKey].tonkho_cuoiky_dukien = tonKho[groupKey].tonkho_cuoiky + tonKho[groupKey].soluong_tang_dukien - tonKho[groupKey].soluong_giam_dukien;
      if (row.type === VATTU_DAUKY) {
        tonKho[groupKey].thoigian_dauky = row.thoi_gian_hoan_thanh;
      }
    }
  });
  return tonKho;
}

export async function bangTongHopThucLuc(tgBatDau, tgKetThuc, idDonvi, vattuIds) {
  let bangTheoDoi = [];
  // Nếu có tgDauky cần tính tồn kho đầu kỳ trước sau đó dùng dữ liệu đấy để tính toán tiếp
  if (tgBatDau) {
    const tonKhoDauKy = await bangTongHopCuoiKy(tgBatDau, idDonvi, vattuIds);
    bangTheoDoi = convertTonKhoCuoiKyThanhTheoDoiDauKy(Object.values(tonKhoDauKy));
  }
  bangTheoDoi = [...bangTheoDoi, ...await tonghopBangTheoDoi(tgBatDau, tgKetThuc, idDonvi, vattuIds)];
  // Sắp xếp các sự kiện theo thứ tự từ gần đến xa
  bangTheoDoi.sort((a, b) => b.thoi_gian_hoan_thanh - a.thoi_gian_hoan_thanh);
  const tonKho = {};
  bangTheoDoi.forEach(row => {
    const groupKey = createGroupKey(row.id_don_vi, row.id_vattu, row.id_tinh_trang);
    // Khi chưa có bản ghi nào thì tạo 1 bản ghi không số liệu
    if (!tonKho[groupKey]) {
      tonKho[groupKey] = {};
      tonKho[groupKey].id_don_vi = row.id_don_vi;
      tonKho[groupKey].id_vattu = row.id_vattu;
      tonKho[groupKey].id_tinh_trang = row.id_tinh_trang;
      tonKho[groupKey].thoigian_dauky = tgBatDau ? tgBatDau : new Date(0);
      tonKho[groupKey].tonkho_dauky = 0;
      tonKho[groupKey].soluong_tang = 0;
      tonKho[groupKey].soluong_tang_dukien = 0;
      tonKho[groupKey].soluong_giam = 0;
      tonKho[groupKey].soluong_giam_dukien = 0;
      tonKho[groupKey].tonkho_cuoiky = 0;
      tonKho[groupKey].soluong_trongkho_tang = 0;
      tonKho[groupKey].soluong_trongkho_giam = 0;
      tonKho[groupKey].soluong_tinhtrang_tang = 0;
      tonKho[groupKey].soluong_tinhtrang_giam = 0;
      tonKho[groupKey].soluong_trongkho_thucte = 0;
      tonKho[groupKey].tonkho_cuoiky_dukien = 0;
      tonKho[groupKey].thoi_gian_hoan_thanh = row.thoi_gian_hoan_thanh; // Lấy sự kiện gần nhất làm thời gian hoàn thành
      tonKho[groupKey].thoigian_cuoiky = tgKetThuc; // Lấy thời gian cuối kỳ trong query làm thời gian cuối kỳ
    }
    // Nếu sự kiện diễn ra sau thời gian đầu kỳ thì lấy giá trị
    if (row.thoi_gian_hoan_thanh >= tonKho[groupKey].thoigian_dauky) {
      tonKho[groupKey].tonkho_dauky = tonKho[groupKey].tonkho_dauky + row.tonkho_dauky;
      tonKho[groupKey].soluong_tang = tonKho[groupKey].soluong_tang + row.soluong_tang;
      tonKho[groupKey].soluong_tang_dukien = tonKho[groupKey].soluong_tang_dukien + (row.soluong_tang_dukien || 0);
      tonKho[groupKey].soluong_giam = tonKho[groupKey].soluong_giam + row.soluong_giam;
      tonKho[groupKey].soluong_trongkho_tang = tonKho[groupKey].soluong_trongkho_tang + (row.soluong_trongkho_tang || 0);
      tonKho[groupKey].soluong_trongkho_giam = tonKho[groupKey].soluong_trongkho_giam + (row.soluong_trongkho_giam || 0);
      tonKho[groupKey].soluong_tinhtrang_tang = tonKho[groupKey].soluong_tinhtrang_tang + (row.soluong_tinhtrang_tang || 0);
      tonKho[groupKey].soluong_tinhtrang_giam = tonKho[groupKey].soluong_tinhtrang_giam + (row.soluong_tinhtrang_giam || 0);
      tonKho[groupKey].soluong_giam_dukien = tonKho[groupKey].soluong_giam_dukien + (row.soluong_giam_dukien || 0);
      tonKho[groupKey].tonkho_cuoiky = tonKho[groupKey].tonkho_dauky + tonKho[groupKey].soluong_tang - tonKho[groupKey].soluong_giam + tonKho[groupKey].soluong_tinhtrang_tang - tonKho[groupKey].soluong_tinhtrang_giam;
      tonKho[groupKey].soluong_trongkho_thucte = tonKho[groupKey].tonkho_cuoiky + tonKho[groupKey].soluong_trongkho_tang - tonKho[groupKey].soluong_trongkho_giam;
      tonKho[groupKey].tonkho_cuoiky_dukien = tonKho[groupKey].tonkho_cuoiky + tonKho[groupKey].soluong_tang_dukien - tonKho[groupKey].soluong_giam_dukien;
      if (row.type === VATTU_DAUKY) {
        tonKho[groupKey].thoigian_dauky = row.thoi_gian_hoan_thanh;
      }
    }
  });
  return tonKho;
}

function convertTonKhoCuoiKyThanhTheoDoiDauKy(tonKho) {
  return tonKho.map(row => {
    return {
      id_don_vi: row.id_don_vi,
      id_vattu: row.id_vattu,
      id_tinh_trang: row.id_tinh_trang,
      tonkho_dauky: row.tonkho_dauky,
      soluong_tang: row.soluong_tang,
      soluong_giam: row.soluong_giam,
      type: VATTU_DAUKY,
      thoi_gian_hoan_thanh: row.thoigian_cuoiky,
    };
  });
}

export async function bangYeuCauDaDuyet(tgDauky, tgCuoiky, idDonvi, danhDiem) {
  async function getAllEvent(idDonvi, tgDauky, tgCuoiky, danhDiem) {
    let queryDieuChuyen = {};
    if (tgDauky || tgCuoiky) {
      queryDieuChuyen.thoi_gian_duyet = {};
      if (tgCuoiky) {
        queryDieuChuyen.thoi_gian_duyet.$lte = new Date(tgCuoiky);
      }
      if (tgDauky) {
        queryDieuChuyen.thoi_gian_duyet.$gte = new Date(tgDauky);
      }
    }
    queryDieuChuyen.trang_thai = TRANG_THAI.YEU_CAU_THANH_CONG;
    queryDieuChuyen.is_deleted = false;
    if (idDonvi) {
      queryDieuChuyen.$or = [
        { id_donvi_di: idDonvi },
        { id_donvi_den: idDonvi },
      ];
    }
    const dieuchuyen = (await DieuchuyenService.getAll(queryDieuChuyen)).map(dc => dc._id);
    const queryYeuCauChiTiet = {};
    queryYeuCauChiTiet.id_yeu_cau_vattu = { $in: dieuchuyen };
    if (danhDiem) {
      queryYeuCauChiTiet.id_danhdiem = danhDiem;
    }
    return YeuCauChiTietService.getForTonKho(queryYeuCauChiTiet);
  }

  const allYeuCau = await getAllEvent(idDonvi, tgDauky, tgCuoiky, danhDiem);

  function convertToTonKhoDanhDiemRows(chitietYeuCau) {
    let bangTheoDoi = [];
    chitietYeuCau.forEach(element => {
      const dieuchuyen = element.id_yeu_cau_vattu;
      const thongtin = {
        id_danh_diem: element.id_danhdiem,
        tonkho_dauky: 0,
        soluong_giam: 0,
        soluong_tang: 0,
        soluong_giam_dukien: 0,
        soluong_tang_dukien: 0,
        thoi_gian_hoan_thanh: dieuchuyen.thoi_gian_duyet,
        type: dieuchuyen.phan_loai,
      };
      if (dieuchuyen.trang_thai === TRANG_THAI.YEU_CAU_THANH_CONG) {
        bangTheoDoi = [...bangTheoDoi, {
          ...thongtin,
          id_don_vi: dieuchuyen.id_donvi_di,
          soluong_giam_dukien: element.soluong,
        }];
        bangTheoDoi = [...bangTheoDoi, {
          ...thongtin,
          id_don_vi: dieuchuyen.id_donvi_den,
          soluong_tang_dukien: element.soluong,
        }];
      }
    });
    return bangTheoDoi;
  }

  return convertToTonKhoDanhDiemRows(allYeuCau);
}

export async function bangTongHopThucLucDanhDiem(tgDauky, tgCuoiky, idDonvi, danhDiem) {
  let vattuIds = undefined;
  if (danhDiem) {
    const vattu = await VatTuService.getAll({ id_danh_diem: danhDiem, is_deleted: false });
    vattuIds = vattu.map((element) => element._id);
  }
  let result = await bangTongHopThucLuc(tgDauky, tgCuoiky, idDonvi, vattuIds);
  result = Object.values(result);
  if (idDonvi) {
    result = result.filter(element => idDonvi.toString() === element.id_don_vi.toString());
  }
  result = await VATTU.populate(result, 'id_vattu');

  function groupTheoDanhDiem(tonkhoTheoMaVattu) {
    const tonKhoDanhDiem = {};

    tonkhoTheoMaVattu.forEach(element => {
      try {
        const groupKey = createGroupKey(element.id_don_vi, element.id_vattu?.id_danh_diem);
        const banGhiHienTai = tonKhoDanhDiem[groupKey];
        if (!banGhiHienTai) {
          tonKhoDanhDiem[groupKey] = {
            ...element,
            id_danh_diem: element.id_vattu?.id_danh_diem,
            id_tinh_trang: element.id_vattu?.id_tinh_trang,
            tinhtrang: [],
          };
        } else {
          tonKhoDanhDiem[groupKey] = {
            ...banGhiHienTai,
            tonkho_dauky: banGhiHienTai.tonkho_dauky + element.tonkho_dauky,
            soluong_tang: banGhiHienTai.soluong_tang + element.soluong_tang,
            soluong_tang_dukien: banGhiHienTai.soluong_tang_dukien + element.soluong_tang_dukien,
            soluong_giam: banGhiHienTai.soluong_giam + element.soluong_giam,
            soluong_giam_dukien: banGhiHienTai.soluong_giam_dukien + element.soluong_giam_dukien,
            tonkho_cuoiky: banGhiHienTai.tonkho_cuoiky + element.tonkho_cuoiky,
            tonkho_cuoiky_dukien: banGhiHienTai.tonkho_cuoiky_dukien + element.tonkho_cuoiky_dukien,
            soluong_trongkho_thucte: banGhiHienTai.soluong_trongkho_thucte + element.soluong_trongkho_thucte,
          };
        }
        tonKhoDanhDiem[groupKey].id = element.id_vattu.id_danh_diem;
        tonKhoDanhDiem[groupKey].tinhtrang = [...tonKhoDanhDiem[groupKey].tinhtrang, element];
      } catch (e) {
        console.log(e);
      }
    });
    return tonKhoDanhDiem;

  }

  result = groupTheoDanhDiem(result);
  return result;
}
