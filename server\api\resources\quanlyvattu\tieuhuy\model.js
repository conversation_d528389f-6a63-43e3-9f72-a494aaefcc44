import mongoose, { Schem<PERSON> } from 'mongoose';
import { DON<PERSON>, NHANVI<PERSON>, THANHLY, TIEUHUY, TOCHUC_QUANLY } from '../../../constant/dbCollections';
import mongoosePaginate from 'mongoose-paginate-v2';
import { TRANG_THAI } from '../../../constant/constant';

const schema = new Schema({
  id_donvi: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: TOCHUC_QUANLY,
  },
  id_nguoi1: {
    type: mongoose.Schema.Types.ObjectId,
    ref: NHANVIEN,
  },
  id_nguoi2: {
    type: mongoose.Schema.Types.ObjectId,
    ref: NHANVIEN,
  },
  id_nguoi3: {
    type: mongoose.Schema.Types.ObjectId,
    ref: NHANVIEN,
  },
  id_nguoi4: {
    type: mongoose.Schema.Types.ObjectId,
    ref: NHANVIEN,
  },
  id_nguoi5: {
    type: mongoose.Schema.Types.ObjectId,
    ref: NHANVIEN,
  },
  thoi_gian_thuc_hien: { type: Date, required: true },
  trang_thai: {
    type: String,
    enum: Object.values(TRANG_THAI),
    default: TRANG_THAI.DANG_THUC_HIEN,
  },
  thoi_gian_hoan_thanh: { type: Date, required: false },
  isActive: { type: Boolean, default: true },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  versionKey: false,
});
schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(TIEUHUY, schema);
