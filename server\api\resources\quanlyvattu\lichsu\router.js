import express from 'express';
import passport from 'passport';
import * as Controller from './controller';
import { authorizationMiddleware } from '../../rbac/middleware';
import LichSuVatTuPermission from '../../rbac/permissions/LichSuVatTuPermission';

export const router = express.Router();
router.use(passport.authenticate('jwt', { session: false }));

router.post('*', authorizationMiddleware([LichSuVatTuPermission.CREATE]));
// nhapdaukyRouter.get('*', authorizationMiddleware([LichSuVatTuPermission.READ]));
router.put('*', authorizationMiddleware([LichSuVatTuPermission.UPDATE]));
router.delete('*', authorizationMiddleware([LichSuVatTuPermission.DELETE]));

router
  .route('/vattu')
  .get(Controller.getAllLichSuVuKhi);

router
  .route('/vattu/:id')
  .get(Controller.getLichSuVattu);

router
  .route('/event/:id')
  .get(Controller.getLichSuEventTheoDonVi);
