import * as ValidatorHelper from '../../../../helpers/validatorHelper';
import GIAONHAN_CHITIET from './model';

const Joi = require('joi');

export async function create(data) {
  const { error, value } = validate(data);
  if (error) throw error;
  return GIAONHAN_CHITIET.create(value);
}

export async function updateAll(chitietUpdate) {
  for (const row of chitietUpdate) {
    const { error, value } = validate(row);
    if (error) throw error;
    await GIAONHAN_CHITIET.findByIdAndUpdate(value._id, value);
  }
}

export function getAll(query) {
  return GIAONHAN_CHITIET.find(query).lean();
}

export function getAllWithInfo(query) {
  return GIAONHAN_CHITIET.find(query)
    .populate('id_nhanvien')
    .populate('id_donvi')
    .populate('id_vattu')
    .populate({ path: 'id_giaonhan', populate: 'id_donvi id_nhanvien' })
    .lean();
}

export function getForTonKho(query) {
  return GIAONHAN_CHITIET.find(query)
    .populate('id_giaonhan')
    .lean();
}

export async function removeAll(query) {
  return GIAONHAN_CHITIET.updateMany(query, { is_deleted: true });
}

const objSchema = Joi.object({
  id_giaonhan: Joi.string().required().messages(ValidatorHelper.messageDefine('Giao nhận')),
  id_nhanvien: Joi.string().messages(ValidatorHelper.messageDefine('Nhân viên')),
  soluong: Joi.number().required().messages(ValidatorHelper.messageDefine('Số lượng')),
  ghichu: Joi.string().required().messages(ValidatorHelper.messageDefine('Ghi chú')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  if (Array.isArray(data)) {
    let validateError = null;
    data.find(itemData => {
      const { value, error } = schema.validate(itemData, { allowUnknown: true, abortEarly: true });
      if (error) validateError = error;
      return error;
    });
    if (validateError && validateError.details) {
      return { validateError };
    }
    return { value: data };
  } else {
    const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
    if (error && error.details) {
      return { error };
    }
    return { value };
  }
}
