import * as ValidatorHelper from '../../../helpers/validatorHelper';

const Joi = require('joi');

const objSchema = Joi.object({
  id_donvi: Joi.string().required().messages(ValidatorHelper.messageDefine('Đơn vị')),
  id_nguoi1: Joi.string().required().messages(ValidatorHelper.messageDefine('Người tham gia 1')),
  id_nguoi2: Joi.string().messages(ValidatorHelper.messageDefine('Người tham gia 2')),
  id_nguoi3: Joi.string().required().messages(ValidatorHelper.messageDefine('Người tham gia 3')),
  thoi_gian_thuc_hien: Joi.date().required().messages(ValidatorHelper.messageDefine('Thời gian kiểm kê')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}
