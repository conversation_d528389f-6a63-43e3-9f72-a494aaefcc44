import * as responseAction from '../../../../utils/responseAction';
import queryHelper from '../../../../helpers/queryHelper';
import * as Service from './yeucauvattu.service';
import * as YeuCauChiTietService from './yeucauvattuchitiet/yeucauvattuchitiet.service';
import CommonError from '../../../../error/CommonError';
import DIEUCHUYENVATTU from '../dieuchuyenvattu.model';
import { LOAI_DIEU_CHUYEN, TRANG_THAI } from '../../../../constant/constant';

const Model = DIEUCHUYENVATTU;

export async function findOne(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findOne({
      _id: id,
      trang_thai: {
        $in: [
          TRANG_THAI.DANG_YEU_CAU,
          TRANG_THAI.YEU_CAU_THANH_CONG,
          TRANG_THAI.YEU_CAU_THAT_BAI,
        ],
      },
      is_deleted: false,
    })
      .populate('id_donvi_di')
      .populate('id_nguoi_di')
      .populate('id_nguoi_den')
      .populate('id_donvi_den').lean();
    if (!data) {
      return responseAction.error(res, CommonError.NOT_FOUND());
    }
    data.chitiet = await YeuCauChiTietService.getAll({ id_yeu_cau_vattu: data._id, is_deleted: false })
      .populate('id_danhdiem').lean();
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function remove(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findOneAndUpdate({ _id: id }, { is_deleted: true }, { new: true }).lean();
    if (!data) {
      return responseAction.error(res, CommonError.NOT_FOUND());
    } else {
      await YeuCauChiTietService.removeAll({ id_yeu_cau_vattu: data._id });
      data.chitiet = await YeuCauChiTietService.getAll({ id_yeu_cau_vattu: data._id });
    }
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function update(req, res) {
  try {
    const { id } = req.params;
    const { error, value } = Service.validate(req.body);
    if (error) return responseAction.error(res, error, 400);
    let data = await Model.findById(id).lean();
    if (!data) {
      return responseAction.error(res, CommonError.NOT_FOUND());
    } else {
      const chitiet = req.body.chitiet || [];
      const chitietUpdate = chitiet.filter(row => row._id);
      await YeuCauChiTietService.updateAll(chitietUpdate);
      let chitietCreate = chitiet.filter(row => !row.hasOwnProperty('_id'));
      chitietCreate.forEach(row => row.id_yeu_cau_vattu = data._id);
      await YeuCauChiTietService.create(chitietCreate);
    }
    if (value.trang_thai === TRANG_THAI.YEU_CAU_THANH_CONG && value.trang_thai != data.trang_thai) {
      await Service.validateTonkho(data);
    }
    data = await Model.findByIdAndUpdate(id, value, { new: true })
      .populate('id_donvi_di')
      .populate('id_nguoi_di')
      .populate('id_nguoi_den')
      .populate('id_donvi_den').lean();
    data.chitiet = await YeuCauChiTietService.getAll({ id_yeu_cau_vattu: data._id, is_deleted: false })
      .populate('id_danhdiem').lean();
    responseAction.success(res, data);
  } catch (err) {
    console.log(err);
    responseAction.error(res, err);
  }
}

export async function create(req, res) {

  try {
    const { error, value } = Service.validate(req.body);
    if (error) return responseAction.error(res, error, 400);
    let data = await Model.create(value);
    if (data && req.body.chitiet && Array.isArray(req.body.chitiet)) {
      data = data.toObject();
      req.body.chitiet.forEach(row => {
        row.id_yeu_cau_vattu = data._id;
      });
      data.chitiet = await YeuCauChiTietService.create(req.body.chitiet);
    }
    return responseAction.success(res, data);
  } catch (err) {
    console.error(err);
    return responseAction.error(res, err, 500);
  }
}

export async function getAll(req, res) {
  try {
    req.query.phan_loai = req.query.phan_loai || LOAI_DIEU_CHUYEN.DIEU_CHUYEN;
    const query = queryHelper.extractQueryParam(req);
    const { criteria, options } = query;
    options.sort = { thoi_gian_yeu_cau: -1 };
    options.populate = [
      { path: 'id_donvi_den' },
      { path: 'id_donvi_di' },
      { path: 'id_nguoi_den' },
      { path: 'id_nguoi_di' },
    ];
    if (!criteria.trang_thai) {
      criteria.trang_thai = {
        $in: [
          TRANG_THAI.DANG_YEU_CAU,
          TRANG_THAI.YEU_CAU_THANH_CONG,
          TRANG_THAI.YEU_CAU_THAT_BAI,
        ],
      };
    }
    const data = await Model.paginate(criteria, options);
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}
