import mongoose, { Schema } from 'mongoose';
import { DAN<PERSON>IEMVATTU, DONVITINH, LOAIVATTU } from '../../../constant/dbCollections';
import mongoosePaginate from 'mongoose-paginate-v2';

const schema = new Schema({
  ma_danh_diem: { type: String, required: true, validate: /\S+/, unique: true, index: true },
  ten_danh_diem: { type: String, required: true, validate: /\S+/, unique: true },
  quan_ly_theo_serial: { type: Boolean, default: false },
  loai_vat_tu_id: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: LOAIVATTU,
  },
  ma_don_vi_tinh: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: DONVITINH,
  },
  isActive: { type: Boolean, default: true },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  versionKey: false,
});

schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(DANHDIEMVATTU, schema);
