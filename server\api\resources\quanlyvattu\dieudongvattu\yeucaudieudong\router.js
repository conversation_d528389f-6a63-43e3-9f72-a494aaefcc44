import express from 'express';
import passport from 'passport';
import * as controller from './controller';
import { authorizationMiddleware } from '../../../rbac/middleware';
import QuanLyVatTuPermisson from '../../../rbac/permissions/quanlyvattu/QuanLyVatTuPermisson';

export const router = express.Router();

router.use(passport.authenticate('jwt', { session: false }));

router.post('*', authorizationMiddleware([QuanLyVatTuPermisson.CREATE]));
router.get('*', authorizationMiddleware([QuanLyVatTuPermisson.READ]));
router.put('/:id', authorizationMiddleware([QuanLyVatTuPermisson.UPDATE]));
router.put('/duyet/:id', authorizationMiddleware([QuanLyVatTuPermisson.CONFIRM]));
router.delete('*', authorizationMiddleware([QuanLyVatTuPermisson.DELETE]));

router
  .route('/')
  .get(controller.getAll)
  .post(controller.create);
router
  .route('/duyet/:id')
  .put(controller.update);
router
  .route('/:id')
  .get(controller.findOne)
  .delete(controller.remove)
  .put(controller.update);
