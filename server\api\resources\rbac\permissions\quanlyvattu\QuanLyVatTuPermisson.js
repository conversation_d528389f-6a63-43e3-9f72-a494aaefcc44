import {create} from "../../permissionHelper";
import resources from "../../Resources";
import actions from "../../Actions";

export default {
  CREATE: create(resources.QUANLY_VATTU, actions.CREATE),
  CONFIRM: create(resources.QUANLY_VATTU, actions.CONFIRM),
  SELECT: create(resources.QUANLY_VATTU, actions.SELECT),
  UPDATE: create(resources.QUANLY_VATTU, actions.UPDATE),
  READ: create(resources.QUANLY_VATTU, actions.READ),
  DELETE: create(resources.QUANLY_VATTU, actions.DELETE)
}
