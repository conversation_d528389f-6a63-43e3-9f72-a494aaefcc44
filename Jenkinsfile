pipeline {
    agent any
    stages {
        stage('Init') {
            steps {
                echo 'Testing..'
            }
        }
        stage ('Deployments') {
            steps {
                echo 'Deploying to Production environment...'
                echo 'Copy project over SSH...'
                sshPublisher(publishers: [
                    sshPublisherDesc(
                        configName: 'swarm1',
                        transfers:
                            [sshTransfer(
                                cleanRemote: false,
                                excludes: '',
                                execCommand: "docker build -t registry.thinklabs.com.vn:5000/qlvtapi ./thinklabsdev/qlvtapiCI/ \
                                    && docker image push registry.thinklabs.com.vn:5000/qlvtapi \
                                    && docker service rm qlvt_qlvtapi || true \
                                    && docker stack deploy -c ./thinklabsdev/qlvtapiCI/qlvtapi-stack.yml qlvt \
                                    && rm -rf ./thinklabsdev/qlvtapiCIB \
                                    && mv ./thinklabsdev/qlvtapiCI/ ./thinklabsdev/qlvtapiCIB",
                                execTimeout: 600000,
                                flatten: false,
                                makeEmptyDirs: false,
                                noDefaultExcludes: false,
                                patternSeparator: '[, ]+',
                                remoteDirectory: './thinklabsdev/qlvtapiCI',
                                remoteDirectorySDF: false,
                                removePrefix: '',
                                sourceFiles: '*, server/'
                            )],
                        usePromotionTimestamp: false,
                        useWorkspaceInPromotion: false,
                        verbose: false
                    )
                ])
            }
        }
    }
}
