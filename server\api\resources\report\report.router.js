import express from 'express';
import passport from 'passport';
import * as Controller from './report.controller';

export const reportRouter = express.Router();
reportRouter.use(passport.authenticate('jwt', { session: false }));

reportRouter
  .route('/tonkhotheovattu')
  .get(Controller.baoCaoTonKhoTheoVatTu);

reportRouter
  .route('/tonkhotheodanhdiem')
  .get(Controller.baoCaoTonKhoTheoDanhDiem);

reportRouter
  .route('/thuclucdonvi')
  .get(Controller.baoCaoThucLucToanDonVi);

reportRouter
  .route('/dieuchuyen')
  .get(Controller.bienBanDieuChuyen);

reportRouter
  .route('/tiepnhan/:id')
  .get(Controller.bienBanTiepNhanVatTu);

reportRouter
  .route('/lichsuvukhi/:id')
  .get(Controller.bienBanLichSuVuKhi);
