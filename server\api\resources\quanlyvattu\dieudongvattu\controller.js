import * as responseAction from '../../../utils/responseAction';
import * as Controller from '../dieuchuyenvattu/dieuchuyenvattu.controller';
import { LOAI_DIEU_CHUYEN } from '../../../constant/constant';

export async function findOne(req, res) {
  try {
    req.query.phan_loai = LOAI_DIEU_CHUYEN.DIEU_DONG;
    return Controller.findOne(req, res);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function remove(req, res) {
  try {
    req.query.phan_loai = LOAI_DIEU_CHUYEN.DIEU_DONG;
    return Controller.remove(req, res);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function update(req, res) {
  try {
    req.query.phan_loai = LOAI_DIEU_CHUYEN.DIEU_DONG;
    req.body.phan_loai = LOAI_DIEU_CHUYEN.DIEU_DONG;
    return Controller.update(req, res);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function create(req, res) {
  try {
    req.body.phan_loai = LOAI_DIEU_CHUYEN.DIEU_DONG;
    return Controller.create(req, res);
  } catch (err) {
    console.error(err);
    return responseAction.error(res, err, 500);
  }
}

export async function getAll(req, res) {
  try {
    req.query.phan_loai = LOAI_DIEU_CHUYEN.DIEU_DONG;
    return Controller.getAll(req, res);
  } catch (err) {
    responseAction.error(res, err);
  }
}
