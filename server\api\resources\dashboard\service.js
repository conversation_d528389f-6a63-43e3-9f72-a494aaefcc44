import * as DieuChuyenService from '../quanlyvattu/dieuchuyenvattu/dieuchuyenvattu.service';
import * as CapNhatTinhTrangService from '../quanlyvattu/capnhattinhtrangvattu/capnhattinhtrangvattu.service';
import * as CapNhatTinhTrangChiTietService from '../quanlyvattu/capnhattinhtrangvattu/chitiet/service';
import * as ThanhLyService from '../quanlyvattu/thanhlyvattu/thanhlyvattu.service';
import * as ThanhLyChiTietService from '../quanlyvattu/thanhlyvattu/chitiet/service';
import * as TiepNhanService from '../quanlyvattu/tiepnhan/service';
import * as TiepNhanChiTietService from '../quanlyvattu/tiepnhan/chitiet/service';
import * as TieuHuyService from '../quanlyvattu/tieuhuy/service';
import * as TieuHuyChiTietService from '../quanlyvattu/tieuhuy/chitiet/service';
import { CAPNHATTINHTRANG, THANHLY, TIEP_NHAN, TIEUHUY, TRANG_THAI } from '../../constant/dbCollections';
import ModelCapNhatTinhTrang from '../quanlyvattu/capnhattinhtrangvattu/chitiet/model';
import ModelThanhLy from '../quanlyvattu/thanhlyvattu/chitiet/model';
import ModelTiepNhan from '../quanlyvattu/tiepnhan/chitiet/model';
import ModelTieuHuy from '../quanlyvattu/tieuhuy/chitiet/model';

// Lịch sử event theo đơn vị
async function getEventDieuChuyenTheoDonVi(id) {
  try {
    const list = await DieuChuyenService.getAll({
      $or: [{ id_donvi_di: id }, { id_donvi_den: id }],
      is_deleted: false,
    });
    // return list
    return list.map(element => {
      return {
        event: element.phan_loai,
        status: element.trang_thai,
      };
    });
  } catch (e) {
    return [];
  }
}

export async function getLichSuEventTheoDonVi(id) {
  let events = [];
  events = [...events, ...await getEventDieuChuyenTheoDonVi(id)];
  events.sort((event1, event2) => {
    return event1.time < event2.time;
  });
  const eventDetail = {};
  events.forEach(row => {
    if (!eventDetail[row.event]) {
      eventDetail[row.event] = {};
      eventDetail[row.event].event = row.event;
      eventDetail[row.event].soluong = 0;
      eventDetail[row.event].thanh_cong = 0;
    }
    eventDetail[row.event].soluong += 1;
    if (row.status === 'THUC_HIEN_THANH_CONG') {
      eventDetail[row.event].thanh_cong += 1;
    }
  });
  return eventDetail;
}


// Số lượng vật tư của các event thanh lý, cập nhật, tiêu hủy, tiếp nhận
async function getCapNhatTinhTrangTheoDonVi(id) {
  try {
    const list = await CapNhatTinhTrangService.getAll({ id_donvi: id, is_deleted: false });
    let listStatusId = [];
    for (let item of list) {
      listStatusId = [...listStatusId, item._id.toString()];
    }
    const dataAll = await CapNhatTinhTrangChiTietService.getAll({ is_deleted: false });
    const filterer = dataAll.filter(item => listStatusId.includes(item.id_capnhat_tinhtrang.toString()));
    let amountSupplies = 0;
    filterer.forEach(item => amountSupplies += item.soluong);
    let event = [];
    event = [...event, { name: 'CAPNHATTINHTRANG', soluong: amountSupplies }];
    return event;
  } catch (e) {
    return [];
  }
}

async function getThanhLyTheoDonVi(id) {
  try {
    const list = await ThanhLyService.getAll({ id_donvi: id, is_deleted: false });
    let listStatusId = [];
    for (let item of list) {
      listStatusId = [...listStatusId, item._id.toString()];
    }
    const dataAll = await ThanhLyChiTietService.getAll({ is_deleted: false });
    const filterer = dataAll.filter(item => listStatusId.includes(item.id_thanhly.toString()));
    let amountSupplies = 0;
    filterer.forEach(item => amountSupplies += item.soluong);

    let event = [];
    event = [...event, { name: 'THANHLY', soluong: amountSupplies }];
    return event;
  } catch (e) {
    return [];
  }
}

async function getTiepNhanTheoDonVi(id) {
  try {
    const list = await TiepNhanService.getAll({ id_donvi: id, is_deleted: false });
    let listStatusId = [];
    for (let item of list) {
      listStatusId = [...listStatusId, item._id.toString()];
    }
    const dataAll = await TiepNhanChiTietService.getAll({ is_deleted: false });
    const filterer = dataAll.filter(item => listStatusId.includes(item.id_tiepnhan.toString()));
    let amountSupplies = 0;
    filterer.forEach(item => amountSupplies += item.soluong);
    let event = [];
    event = [...event, { name: 'TIEPNHAN', soluong: amountSupplies }];
    return event;
  } catch (e) {
    return [];
  }
}

async function getTieuHuyTheoDonVi(id) {
  try {
    const list = await TieuHuyService.getAll({ id_donvi: id, is_deleted: false });
    let listStatusId = [];
    for (let item of list) {
      listStatusId = [...listStatusId, item._id.toString()];
    }
    const dataAll = await TieuHuyChiTietService.getAll({ is_deleted: false });
    const filterer = dataAll.filter(item => listStatusId.includes(item.id_tieuhuy.toString()));
    let amountSupplies = 0;
    filterer.forEach(item => amountSupplies += item.soluong);
    let event = [];
    event = [...event, { name: 'TIEUHUY', soluong: amountSupplies }];
    return event;
  } catch (e) {
    return [];
  }
}

export async function getEventDetailsTheoDonVi(id) {
  let events = [];
  events = [...events, ...await getCapNhatTinhTrangTheoDonVi(id)];
  events = [...events, ...await getThanhLyTheoDonVi(id)];
  events = [...events, ...await getTiepNhanTheoDonVi(id)];
  events = [...events, ...await getTieuHuyTheoDonVi(id)];
  events.sort((event1, event2) => {
    return event1.time < event2.time;
  });

  return events;
}
