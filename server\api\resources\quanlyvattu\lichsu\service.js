import * as VattuDauKyService from '../vattudauky/vattudauky.service';
import * as DieuChuyenChitietService from '../dieuchuyenvattu/dieuchuyenvattuchitiet/dieuchuyenvattuchitiet.service';
import * as DieuChuyenService from '../dieuchuyenvattu/dieuchuyenvattu.service';
import * as KiemKeChitietService from '../kiemkevattuchitiet/kiemkevattuchitiet.service';
import * as SuaChuaChitietService from '../suachuavattuchitiet/suachuavattuchitiet.service';
import * as SuaChuaService from '../suachuavattu/suachuavattu.service';
import * as ThanhLyChitietService from '../thanhlyvattu/chitiet/service';
import * as ThanhLyService from '../thanhlyvattu/thanhlyvattu.service';
import * as TiepNhanChitietService from '../tiepnhan/chitiet/service';
import * as TiepNhanService from '../tiepnhan/service';
import * as TieuHuyChitietService from '../tieuhuy/chitiet/service';
import * as TieuHuyService from '../tieuhuy/service';
import * as BanGiaoCaNhanChitietService from '../bangiaocanhan/chitiet/service';
import * as BanGiaoCaNhanService from '../bangiaocanhan/service';
import * as HoanTraChitietService from '../travattu/chitiet/service';
import * as HoanTraService from '../travattu/service';
import {
  KIEMKE,
  SUACHUA,
  THANHLY,
  TIEP_NHAN,
  TIEUHUY,
  VATTU_DAUKY,
  GIAOVATTU_CANHAN,
  TRA_VATTU,
} from '../../../constant/dbCollections';
import { TRANG_THAI, TRANG_THAI_VAT_TU } from '../../../constant/constant';


async function getEventFromTieuHuy(id) {
  try {
    const list = await TieuHuyChitietService.getAllWithInfo({ id_vattu: id, is_deleted: false });
    return list.map(element => {
      return {
        event: TIEUHUY,
        time: element.id_tieuhuy.thoi_gian_thuc_hien,
        info: element,
      };
    });
  } catch (e) {
    return [];
  }
}

async function getEventFromTiepNhan(id) {
  try {
    const list = await TiepNhanChitietService.getAllWithInfo({ id_vattu: id, is_deleted: false });
    return list.map(element => {
      return {
        event: TIEP_NHAN,
        time: element.id_tiepnhan.thoi_gian_thuc_hien,
        info: element,
      };
    });
  } catch (e) {
    return [];
  }
}

async function getEventFromThanhLy(id) {
  try {
    const list = await ThanhLyChitietService.getAllWithInfo({ id_vattu: id, is_deleted: false });
    return list.map(element => {
      return {
        event: THANHLY,
        time: element.id_thanhly.thoi_gian_thuc_hien,
        info: element,
      };
    });
  } catch (e) {
    return [];
  }
}

async function getEventFromSuaChua(id) {
  try {
    let list = await SuaChuaChitietService.getAllWithInfo({ id_vattu: id, is_deleted: false });
    list = list.filter(element => !!element.id_suachua);
    return list.map(element => {
      return {
        event: SUACHUA,
        time: element.id_suachua.thoi_gian_thuc_hien,
        info: element,
      };
    });
  } catch (e) {
    return [];
  }
}

async function getEventFromKiemKe(id) {
  try {
    const list = await KiemKeChitietService.getAllWithInfo({ id_vattu: id, is_deleted: false });
    return list.map(element => {
      return {
        event: KIEMKE,
        time: element.id_kiemke.thoi_gian_thuc_hien,
        info: element,
      };
    });
  } catch (e) {
    return [];
  }
}

async function getEventFromDieuChuyen(id) {
  try {
    const list = await DieuChuyenChitietService.getAllWithInfo({ id_vattu: id, is_deleted: false });
    return list.map(element => {
      return {
        event: element.id_dieu_chuyen.phan_loai,
        time: element.id_dieu_chuyen.thoi_gian_thuc_hien,
        info: element,
      };
    });
  } catch (e) {
    return [];
  }
}

async function getEventFromDauKy(id) {
  try {
    const list = await VattuDauKyService.getAllChiTiet({ id_vattu: id, is_deleted: false });
    return list.map(element => {
      return {
        event: VATTU_DAUKY,
        time: element.thoigian_dauky,
        info: element,
      };
    });
  } catch (e) {
    return [];
  }
}

async function getEventFromBanGiaoCaNhan(id) {
  try {
    const list = await BanGiaoCaNhanChitietService.getAllWithInfo({ id_vattu: id, is_deleted: false });
    return list.map(element => {
      return {
        event: GIAOVATTU_CANHAN,
        time: element.id_giaonhan.thoi_gian_ban_giao_ca_nhan,
        info: element,
      };
    });
  } catch (e) {
    return [];
  }
}

async function getEventFromHoanTra(id) {
  try {
    const list = await HoanTraChitietService.getAllWithInfo({ id_vattu: id, is_deleted: false });
    return list.map(element => {
      return {
        event: TRA_VATTU,
        time: element.id_travattu.thoi_gian_tra_vat_tu,
        info: element,
      };
    });
  } catch (e) {
    return [];
  }
}

export async function getLichSuVattu(id) {
  let events = [];
  events = [...events, ...await getEventFromDauKy(id)];
  events = [...events, ...await getEventFromDieuChuyen(id)];
  events = [...events, ...await getEventFromKiemKe(id)];
  events = [...events, ...await getEventFromSuaChua(id)];
  events = [...events, ...await getEventFromThanhLy(id)];
  events = [...events, ...await getEventFromTiepNhan(id)];
  events = [...events, ...await getEventFromTieuHuy(id)];
  events = [...events, ...await getEventFromBanGiaoCaNhan(id)];
  events = [...events, ...await getEventFromHoanTra(id)];
  events.sort((event1, event2) => {
    return event1.time < event2.time;
  });
  return events;
}

// Lịch sử event theo đơn vị
async function getEventFromDauKyTheoDonVi(id) {
  try {
    const list = await VattuDauKyService.getAll({ id_don_vi: id, is_deleted: false });
    return list.map(element => {
      return {
        event: VATTU_DAUKY,
        time: element.thoigian_dauky,
        info: element,
      };
    });
  } catch (e) {
    return [];
  }
}

async function getEventDieuChuyenTheoDonVi(id) {
  try {
    const list = await DieuChuyenService.getAll({
      $or: [{ id_donvi_di: id }, { id_donvi_den: id }],
      trang_thai: TRANG_THAI.THUC_HIEN_THANH_CONG,
      is_deleted: false,
    });
    // return list
    return list.map(element => {
      return {
        event: element.phan_loai,
        info: element,
        time: element.thoi_gian_hoan_thanh,
      };
    });
  } catch (e) {
    return [];
  }
}

async function getEventSuaChuaTheoDonVi(id) {
  try {
    const list = await SuaChuaService.getAll({ id_donvi: id, is_deleted: false });
    return list.map(element => {
      return {
        event: SUACHUA,
        info: element,
        time: element.thoi_gian_hoan_thanh,
      };
    });
  } catch (e) {
    return [];
  }
}

async function getEventThanhLyTheoDonVi(id) {
  try {
    const list = await ThanhLyService.getAll({ id_donvi: id, is_deleted: false });
    return list.map(element => {
      return {
        event: THANHLY,
        info: element,
        time: element.thoi_gian_hoan_thanh,
      };
    });
  } catch (e) {
    return [];
  }
}

async function getEventTiepNhanTheoDonVi(id) {
  try {
    const list = await TiepNhanService.getAll({ id_donvi: id, is_deleted: false });
    return list.map(element => {
      return {
        event: TIEP_NHAN,
        info: element,
        time: element.thoi_gian_hoan_thanh,
      };
    });
  } catch (e) {
    return [];
  }
}

async function getEventTieuHuyTheoDonVi(id) {
  try {
    const list = await TieuHuyService.getAll({ id_donvi: id, is_deleted: false });
    return list.map(element => {
      return {
        event: TIEUHUY,
        info: element,
        time: element.thoi_gian_hoan_thanh,
      };
    });
  } catch (e) {
    return [];
  }
}

async function getEventBanGiaoCaNhanTheoDonVi(id) {
  try {
    const list = await BanGiaoCaNhanService.getAll({ id_donvi: id, is_deleted: false });
    return list.map(element => {
      return {
        event: GIAOVATTU_CANHAN,
        info: element,
        time: element.thoi_gian_hoan_thanh,
      };
    });
  } catch (e) {
    return [];
  }
}

async function getEventHoanTraTheoDonVi(id) {
  try {
    const list = await HoanTraService.getAll({ id_donvi: id, is_deleted: false });
    return list.map(element => {
      return {
        event: TRA_VATTU,
        info: element,
        time: element.thoi_gian_hoan_thanh,
      };
    });
  } catch (e) {
    return [];
  }
}

export async function getLichSuEventTheoDonVi(id) {
  let events = [];
  // events = [...events, ...await getEventFromDauKyTheoDonVi(id)];
  events = [...events, ...await getEventDieuChuyenTheoDonVi(id)];
  // events = [...events, ...await getEventFromKiemKe(id)];
  // events = [...events, ...await getEventSuaChuaTheoDonVi(id)];
  events = [...events, ...await getEventThanhLyTheoDonVi(id)];
  events = [...events, ...await getEventTiepNhanTheoDonVi(id)];
  events = [...events, ...await getEventTieuHuyTheoDonVi(id)];
  events = [...events, ...await getEventBanGiaoCaNhanTheoDonVi(id)];
  events = [...events, ...await getEventHoanTraTheoDonVi(id)];
  events.sort((event1, event2) => {
    return event1.time < event2.time;
  });
  return events;
}
