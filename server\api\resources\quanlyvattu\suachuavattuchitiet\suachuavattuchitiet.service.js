import * as ValidatorHelper from '../../../helpers/validatorHelper';
import SUACHUA_CHITIET from './suachuavattuchitiet.model';
import KIEMKE_CHITIET from '../../../constant/dbCollections';

const Joi = require('joi');

export async function create(data) {
  const { error, value } = validate(data);
  if (error) throw error;
  return SUACHUA_CHITIET.create(value);
}

export async function updateAll(chitietUpdate) {
  for (const row of chitietUpdate) {
    const { error, value } = validate(row);
    if (error) throw error;
    await SUACHUA_CHITIET.findByIdAndUpdate(value._id, value);
  }
}

export function getAll(query) {
  return SUACHUA_CHITIET.find(query).lean();
}

export function getAllWithInfo(query) {
  return SUACHUA_CHITIET.find(query)
    .populate('id_vattu')
    .populate({ path: 'id_suachua', populate: 'id_donvi' })
    .lean();
}

export async function removeAll(query) {
  return SUACHUA_CHITIET.updateMany(query, { is_deleted: true });
}

const objSchema = Joi.object({
  id_suachua: Joi.string().required().messages(ValidatorHelper.messageDefine('Sửa chữa')),
  id_vattu: Joi.string().required().messages(ValidatorHelper.messageDefine('Vật tư')),
  id_kho: Joi.string().messages(ValidatorHelper.messageDefine('Kho')),
  id_tinhtrang: Joi.string().required().messages(ValidatorHelper.messageDefine('Tình trạng')),
  soluong: Joi.number().required().messages(ValidatorHelper.messageDefine('Số lượng')),
  ghichu: Joi.string().required().messages(ValidatorHelper.messageDefine('Ghi chú')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  if (Array.isArray(data)) {
    let validateError = null;
    data.find(itemData => {
      const { value, error } = schema.validate(itemData, { allowUnknown: true, abortEarly: true });
      if (error) validateError = error;
      return error;
    });
    if (validateError && validateError.details) {
      return { validateError };
    }
    return { value: data };
  } else {
    const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
    if (error && error.details) {
      return { error };
    }
    return { value };
  }
}
