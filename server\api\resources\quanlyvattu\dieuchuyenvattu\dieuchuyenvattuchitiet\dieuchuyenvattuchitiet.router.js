import express from 'express';
import passport from 'passport';
import * as dieuchuyenvattuchitietController from './dieuchuyenvattuchitiet.controller';
import { authorizationMiddleware } from '../../../rbac/middleware';
import QuanLyVatTuPermisson from '../../../rbac/permissions/quanlyvattu/QuanLyVatTuPermisson';

export const dieuchuyenvattuchitietRouter = express.Router();
dieuchuyenvattuchitietRouter.use(passport.authenticate('jwt', { session: false }));

dieuchuyenvattuchitietRouter.post('*', authorizationMiddleware([QuanLyVatTuPermisson.CREATE]));
dieuchuyenvattuchitietRouter.get('*', authorizationMiddleware([QuanLyVatTuPermisson.READ]));
dieuchuyenvattuchitietRouter.put('*', authorizationMiddleware([QuanLyVatTuPermisson.UPDATE]));
dieuchuyenvattuchitietRouter.delete('*', authorizationMiddleware([QuanLyVatTuPermisson.DELETE]));
dieuchuyenvattuchitietRouter
  .route('/')
  .get(dieuchuyenvattuchitietController.getAll)
  .post(dieuchuyenvattuchitietController.create);

dieuchuyenvattuchitietRouter
  .route('/:id')
  .get(dieuchuyenvattuchitietController.findOne)
  .delete(dieuchuyenvattuchitietController.remove)
  .put(dieuchuyenvattuchitietController.update);
