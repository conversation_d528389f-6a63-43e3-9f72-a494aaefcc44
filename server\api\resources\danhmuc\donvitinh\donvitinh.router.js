import express from 'express';
import passport from 'passport';
import * as donvitinhController from './donvitinh.controller';
import { authorizationMiddleware } from '../../rbac/middleware';
import DanhMucPermission from '../../rbac/permissions/danhmuc/DanhMucPermission';


export const donvitinhRouter = express.Router();

donvitinhRouter.use(passport.authenticate('jwt', { session: false }));
donvitinhRouter.get('*', authorizationMiddleware([DanhMucPermission.READ]));
donvitinhRouter.post('*', authorizationMiddleware([DanhMucPermission.CREATE]));
donvitinhRouter.put('*', authorizationMiddleware([DanhMucPermission.UPDATE]));
donvitinhRouter.delete('*', authorizationMiddleware([DanhMucPermission.DELETE]));

donvitinhRouter
  .route('/')
  .get(donvitinhController.getAll)
  .post(donvitinhController.create);
donvitinhRouter
  .route('/:id')
  .get(donvitinhController.findOne)
  .delete(donvitinhController.remove)
  .put(donvitinhController.update);
