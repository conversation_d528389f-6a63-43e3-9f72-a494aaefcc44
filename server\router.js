import express from 'express';

import userRouter from './api/resources/user/user.router';
import { dashboardRouter } from './api/resources/dashboard/router';
import { loaiVattuRouter } from './api/resources/danhmuc/loaivattu/loaivattu.router';
import { vattuRouter } from './api/resources/danhmuc/vattu/vattu.router';
import { giayphepvattuRouter } from './api/resources/danhmuc/giayphepvattu/giayphepvattu.router';
import { donviRouter } from './api/resources/danhmuc/donvi/donvi.router';
import { toChucQuanLyRouter } from './api/resources/tochucquanly/donVi.router';
import { tinhtrangRouter } from './api/resources/danhmuc/tinhtrang/tinhtrang.router';
import { donvitinhRouter } from './api/resources/danhmuc/donvitinh/donvitinh.router';
import { khovattuRouter } from './api/resources/danhmuc/khovattu/khovattu.router';
import { nhanvienRouter } from './api/resources/danhmuc/nhanvien/nhanvien.router';
import { nhomvattuRouter } from './api/resources/danhmuc/nhomvattu/nhomvattu.router';
import { danhdiemRouter } from './api/resources/danhmuc/danhdiemvattu/danhdiem.router';
import { nguoncapRouter } from './api/resources/danhmuc/nguoncap/nguoncap.router';
import { vattuDauKyRouter } from './api/resources/quanlyvattu/vattudauky/vattudauky.router';
import { tonkhoRouter } from './api/resources/quanlyvattu/tonkho/tonkho.router';
import { yeucauvattuRouter } from './api/resources/quanlyvattu/dieuchuyenvattu/yeucauvattu/yeucauvattu.router';
import { yeucauvattuchitietRouter } from './api/resources/quanlyvattu/dieuchuyenvattu/yeucauvattu/yeucauvattuchitiet/yeucauvattuchitiet.router';
import { dieuchuyenvattuRouter } from './api/resources/quanlyvattu/dieuchuyenvattu/dieuchuyenvattu.router';
import { dieuchuyenvattuchitietRouter } from './api/resources/quanlyvattu/dieuchuyenvattu/dieuchuyenvattuchitiet/dieuchuyenvattuchitiet.router';
import { kiemkevattuRouter } from './api/resources/quanlyvattu/kiemkevattu/kiemkevattu.router';
import { kiemkevattuchitietRouter } from './api/resources/quanlyvattu/kiemkevattuchitiet/kiemkevattuchitiet.router';
import { suachuavattuRouter } from './api/resources/quanlyvattu/suachuavattu/suachuavattu.router';
import { suachuavattuchitietRouter } from './api/resources/quanlyvattu/suachuavattuchitiet/suachuavattuchitiet.router';
import { thanhlyvattuRouter } from './api/resources/quanlyvattu/thanhlyvattu/thanhlyvattu.router';
import { router as thanhlychitietRouter } from './api/resources/quanlyvattu/thanhlyvattu/chitiet/router';
import { router as bangiaovtRouter } from './api/resources/quanlyvattu/bangiaovattu/router';
import { router as yeuCauBanGiaovtRouter } from './api/resources/quanlyvattu/bangiaovattu/yeucaubangiao/router';
import { router as dieudongvtRouter } from './api/resources/quanlyvattu/dieudongvattu/router';
import { router as yeuCauDieudongRouter } from './api/resources/quanlyvattu/dieudongvattu/yeucaudieudong/router';
import { router as tiepnhanRouter } from './api/resources/quanlyvattu/tiepnhan/router';
import { router as tiepnhanchitietRouter } from './api/resources/quanlyvattu/tiepnhan/chitiet/router';
import { router as tieuhuyRouter } from './api/resources/quanlyvattu/tieuhuy/router';
import { router as tieuhuychitietRouter } from './api/resources/quanlyvattu/tieuhuy/chitiet/router';
import { router as bangiaocanhanRouter } from './api/resources/quanlyvattu/bangiaocanhan/router';
import { router as bangiaocanhanchitietRouter } from './api/resources/quanlyvattu/bangiaocanhan/chitiet/router';
import { router as travattuRouter } from './api/resources/quanlyvattu/travattu/router';
import { router as travattuchitietRouter } from './api/resources/quanlyvattu/travattu/chitiet/router';
import { router as lichsuRouter } from './api/resources/quanlyvattu/lichsu/router';
import { capnhattinhtrangvtRouter } from './api/resources/quanlyvattu/capnhattinhtrangvattu/capnhattinhtrangvattu.router';
import { capnhattinhtrangvtchitietRouter } from './api/resources/quanlyvattu/capnhattinhtrangvattu/chitiet/router';
import { roleRouter } from './api/resources/role/role.router';
import { reportRouter } from './api/resources/report/report.router';
import { fileRouter } from './api/resources/file/file.router';


const router = express.Router();

router.use('/file', fileRouter);
router.use('/users', userRouter);
router.use('/role', roleRouter);
router.use('/dashboard', dashboardRouter);
router.use('/loaivattu', loaiVattuRouter);
router.use('/vattu', vattuRouter);
router.use('/giayphepvattu', giayphepvattuRouter);
router.use('/donvi', donviRouter);
router.use('/tinhtrang', tinhtrangRouter);
router.use('/donvitinh', donvitinhRouter);
router.use('/khovattu', khovattuRouter);
router.use('/nhanvien', nhanvienRouter);
router.use('/nhomvattu', nhomvattuRouter);
router.use('/danhdiemvattu', danhdiemRouter);
router.use('/nguoncap', nguoncapRouter);
router.use('/vattudauky', vattuDauKyRouter);
router.use('/tonkho', tonkhoRouter);
router.use('/yeucauvattu', yeucauvattuRouter);
router.use('/yeucauvattuchitiet', yeucauvattuchitietRouter);
router.use('/dieuchuyenvt', dieuchuyenvattuRouter);
router.use('/dieuchuyenvtchitiet', dieuchuyenvattuchitietRouter);
router.use('/kiemkevt', kiemkevattuRouter);
router.use('/kiemkevtchitiet', kiemkevattuchitietRouter);
router.use('/suachua', suachuavattuRouter);
router.use('/suachuachitiet', suachuavattuchitietRouter);
router.use('/thanhly', thanhlyvattuRouter);
router.use('/thanhlychitiet', thanhlychitietRouter);
router.use('/bangiaovt', bangiaovtRouter);
router.use('/yeucaubangiaovt', yeuCauBanGiaovtRouter);
router.use('/dieudongvt', dieudongvtRouter);
router.use('/yeucaudieudongvt', yeuCauDieudongRouter);
router.use('/tiepnhan', tiepnhanRouter);
router.use('/tiepnhanchitiet', tiepnhanchitietRouter);
router.use('/tieuhuy', tieuhuyRouter);
router.use('/tieuhuychitiet', tieuhuychitietRouter);
router.use('/bangiaovattucanhan', bangiaocanhanRouter);
router.use('/bangiaocanhanchitiet', bangiaocanhanchitietRouter);
router.use('/travattu', travattuRouter);
router.use('/travattuchitiet', travattuchitietRouter);
router.use('/lichsu', lichsuRouter);
router.use('/capnhattinhtrangvt', capnhattinhtrangvtRouter);
router.use('/capnhattinhtrangvtchitiet', capnhattinhtrangvtchitietRouter);
router.use('/report', reportRouter);
router.use('/tochucquanly', toChucQuanLyRouter);

module.exports = router;
