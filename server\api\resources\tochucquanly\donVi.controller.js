import * as responseAction from '../../utils/responseAction';
import queryHelper from '../../helpers/queryHelper';
import * as Service from './donVi.service';
import { buildTree } from './donVi.service';
import Model from './donVi.model';

export async function findOne(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findById(id);
    if (!data) {
      return responseAction.error(res, 404, '');
    }
    return responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function remove(req, res) {
  try {
    const { id } = req.params;

    const parentUnit = await Model.findOne({ don_vi_cha_id: id, is_deleted: false });
    if (parentUnit) {
      return responseAction.error(res, { message: 'Xóa đơn vị con trước khi x<PERSON>a đơn vị cha' }, 400);
    }

    const data = await Model.findOneAndUpdate({ _id: id, is_root: false }, { is_deleted: true }, { new: true });
    if (!data) {
      return responseAction.error(res, null, 404);
    }
    return responseAction.success(res, data);

  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function update(req, res) {
  try {
    const { id } = req.params;
    const { error, value } = Service.validate(req.body);
    if (error) return responseAction.error(res, error, 400);
    const isUnique = await Model.findOne({
      ma_don_vi: value.ma_don_vi,
      is_deleted: false,
      _id: { $ne: value._id },
    }, { _id: 1 });
    if (isUnique) {
      return responseAction.error(res, { message: 'Mã đơn vị đã tồn tại, vui lòng kiểm tra và thử lại' }, 400);
    }
    const data = await Model.findOneAndUpdate({ _id: id }, value, { new: true })
      .populate({ path: 'don_vi_cha_id', select: 'ten_don_vi' });
    if (!data) {
      return responseAction.error(res, null, 404);
    }
    return responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function create(req, res) {

  try {
    const { error, value } = Service.validate(req.body);
    if (error) return responseAction.error(res, error, 400);
    const isUnique = await Model.findOne({ ma_don_vi: value.ma_don_vi, is_deleted: false }, { _id: 1 });
    if (isUnique) {
      return responseAction.error(res, { message: 'Mã đơn vị đã tồn tại, vui lòng kiểm tra và thử lại' }, 400);
    }
    const data = await Model.create(value);
    let dataRtn = await data
      .populate({ path: 'don_vi_cha_id', select: 'ten_don_vi' }).execPopulate();
    return responseAction.success(res, dataRtn);
  } catch (err) {
    console.error(err);
    return responseAction.error(res, err, 500);
  }
}

export async function getAll(req, res) {
  try {
    if (req.query.tree) {
      const allAvailableDonVi = await Model.find({
        is_deleted: false,
        _id: await Service.getDonViQuery(req, null, true),
      }).lean();
      let congTyTreeForAdmin = buildTree(allAvailableDonVi);
      return responseAction.success(res, congTyTreeForAdmin);
    } else {
      const query = queryHelper.extractQueryParam(req, ['ma_don_vi', 'ten_don_vi']);
      const { criteria, options } = query;
      criteria._id = await Service.getDonViQuery(req, null, false);
      options.populate = [
        { path: 'don_vi_cha_id', select: 'ten_don_vi' },
      ];
      options.sort = { created_at: 1, ten_don_vi: 1 };
      const data = await Model.paginate(criteria, options);
      responseAction.success(res, data);
    }
  } catch (err) {
    console.log(err);
    responseAction.error(res, err);
  }
}
