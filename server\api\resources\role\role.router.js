import { Router } from 'express';
import * as RoleCtr from './role.controller';
import passport from 'passport';
import { authorizationMiddleware } from '../rbac/middleware';
import VaiTroPermission from '../rbac/permissions/role/VaiTroPermission';

export const roleRouter = Router();
roleRouter.use(passport.authenticate('jwt', { session: false }));
roleRouter.get('*', authorizationMiddleware([VaiTroPermission.READ]));
roleRouter.post('*', authorizationMiddleware([VaiTroPermission.CREATE]));
roleRouter.put('*', authorizationMiddleware([VaiTroPermission.UPDATE]));
roleRouter.delete('*', authorizationMiddleware([VaiTroPermission.DELETE]));

roleRouter
  .route('/')
  .post(RoleCtr.create)
  .delete(RoleCtr.deleteALl)
  .get(RoleCtr.getAll);
roleRouter
  .route('/:id')
  .get(RoleCtr.getById)
  .delete(RoleCtr.deleteById)
  .put(RoleCtr.UpdateById);

