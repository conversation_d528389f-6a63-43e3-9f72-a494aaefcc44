import * as responseAction from '../../../utils/responseAction';
import queryHelper from '../../../helpers/queryHelper';
import * as Service from './vattu.service';
import Model from './vattu.model';
import ModelDanhDiem from '../danhdiemvattu/danhdiem.model';
import ModelDonViTinh from '../donvitinh/donvitinh.model';
import ModelNguonCap from '../nguoncap/nguoncap.model';
import ModelGiapPhep from '../giayphepvattu/giayphepvattu.model';
import { filterRequest, optionsRequest } from '../../../utils/filterRequest';
import User from '../../user/user.model';
import * as VatTuDauKyService from '../../quanlyvattu/vattudauky/vattudauky.service';
import * as TiepNhanChiTietService from '../../quanlyvattu/tiepnhan/chitiet/service';

const excelToJson = require('convert-excel-to-json');


export async function findOne(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findById(id);
    if (!data) {
      return responseAction.error(res, 404, '');
    }
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function remove(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findOneAndUpdate({ _id: id }, { is_deleted: true }, { new: true });
    if (!data) {
      return responseAction.error(res, 404, '');
    }
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function update(req, res) {
  try {
    const { id } = req.params;
    const { error, value } = Service.validate(req.body);
    if (error) return responseAction.error(res, error, 400);
    if (value.serial) {
      const isUniqueSeri = await Model.findOne({
        $and: [{ id_danh_diem: value.id_danh_diem }, { serial: value.serial }],
        is_deleted: false, _id: { $ne: value._id },
      }, { _id: 1 });
      if (isUniqueSeri) {
        return responseAction.error(res, { message: 'Serial vật tư đã tồn tại, vui lòng kiểm tra và thử lại' }, 400);
      }
    }

    const data = await Model.findOneAndUpdate({ _id: id }, value, { new: true })
      .populate({ path: 'id_danh_diem', populate: { path: 'ma_don_vi_tinh' } })
      .populate({ path: 'id_nguon_cap', select: 'ten_nguon_cap' });
    if (!data) {
      return responseAction.error(res, null, 404);
    }
    return responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function create(req, res) {
  try {
    const { error, value } = Service.validate(req.body);
    if (error) return responseAction.error(res, error, 400);
    if (value.serial) {
      const isUniqueSerial = await Model.findOne({
          $and: [{ id_danh_diem: value.id_danh_diem }, { serial: value.serial }],
          is_deleted: false,
        },
        { _id: 1 });
      if (isUniqueSerial) {
        return responseAction.error(res, { message: 'Serial vật tư đã tồn tại, vui lòng kiểm tra và thử lại' }, 400);
      }
    }
    const data = await Model.create(value);
    let dataRtn = await data
      .populate({ path: 'id_danh_diem', populate: { path: 'ma_don_vi_tinh' } })
      .populate({ path: 'id_nguon_cap', select: 'ten_nguon_cap' }).execPopulate();
    return responseAction.success(res, dataRtn);
  } catch (err) {
    console.error(err);
    return responseAction.error(res, err, 500);
  }
}

export async function getAll(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria, options } = query;
    options.populate = [
      { path: 'id_danh_diem', populate: { path: 'ma_don_vi_tinh' } },
      { path: 'id_nguon_cap', select: 'ten_nguon_cap' },
    ];
    options.sort = { ten_vat_tu: 1 };
    const data = await Model.paginate(criteria, options);
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function getAllUsed(req, res) {
  try {
    const dataDauKy = await VatTuDauKyService.getAll();
    const dataTiepNhan = await TiepNhanChiTietService.getAll();
    let existIds = [];
    for (let item of dataDauKy) {
      existIds = [...existIds, item];
    }
    for (let item of dataTiepNhan) {
      existIds = [...existIds, item];
    }
    const query = queryHelper.extractQueryParam(req);
    const { criteria, options } = query;
    options.populate = [
      { path: 'id_danh_diem', select: 'ma_don_vi_tinh' },
      { path: 'id_nguon_cap', select: 'ten_nguon_cap' },
    ];
    options.sort = { ten_vat_tu: 1 };
    criteria._id = {
      $nin: existIds.map(item => item.id_vattu),
    };
    const dataAll = await Model.paginate(criteria, options);
    responseAction.success(res, dataAll);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function importFile(req, res) {
  let files = req.files;
  //Convert dữ liệu từ xlsx sang Json
  const excelData = excelToJson({
    sourceFile: files.file.path,
    name: 'docs',
    header: {
      rows: 1,
    },
    columnToKey: {
      // A: 'key',
      B: 'id_danh_diem',
      C: 'ten_vat_tu',
      D: 'ma_vat_tu',
      E: 'serial',
      F: 'so_lo',
      G: 'ma_don_vi_tinh',
      H: 'so_giay_phep',
      I: 'id_nguon_cap',
      J: 'ngay_cap',
      K: 'han_su_dung',
      L: 'nuoc_san_xuat',
      M: 'dong_bo_kem_theo',
    },
  });

  const dataInFile = excelData.Sheet1;

  //Check mã vật tư
  const existCode = await Model.find({ ma_vat_tu: { $in: dataInFile.map(item => item.ma_vat_tu) }, is_deleted: false });
  //Trả về các bản ghi trong db có mã vật tư tồn tại trong dữ liệu tải lên
  const mapSuppliesCode = {};
  existCode.forEach(row => {
    mapSuppliesCode[row.ma_vat_tu] = row._id;
  });

  // Check id danh điểm
  const existSuppliesCategory = await ModelDanhDiem.find({
    ten_danh_diem: { $in: dataInFile.map(item => item.id_danh_diem) },
    is_deleted: false,
  });
  const mapSuppliesCategory = {};
  existSuppliesCategory.forEach(row => {
    mapSuppliesCategory[row.ten_danh_diem] = row._id;
  });

  //Check số giấy phép
  const existLicense = await ModelGiapPhep.find({
    so_giay_phep: { $in: dataInFile.map(item => item.so_giay_phep) },
    is_deleted: false,
  });
  const mapLicense = {};
  existLicense.forEach(row => {
    mapLicense[row.so_giay_phep] = row._id;
  });

  //Check id nguồn cấp
  const existPoweredBy = await ModelNguonCap.find({
    ten_nguon_cap: { $in: dataInFile.map(item => item.id_nguon_cap) },
    is_deleted: false,
  });
  const mapPoweredBy = {};
  existPoweredBy.forEach(row => {
    mapPoweredBy[row.ten_nguon_cap] = row._id;
  });

  //Check đơn vị tính
  const existCalUnit = await ModelDonViTinh.find({
    ten_don_vi_tinh: { $in: dataInFile.map(item => item.ma_don_vi_tinh) },
    is_deleted: false,
  });
  const mapCalUnit = {};
  existCalUnit.forEach(row => {
    mapCalUnit[row.ten_don_vi_tinh] = row._id;
  });

  // Check serial
  const existSerial = await Model.find({ serial: { $in: dataInFile.map(item => item.serial) }, is_deleted: false });
  const mapSerial = {};
  existSerial.forEach(row => {
    mapSerial[row.serial] = row.serial;
  });

  //Group record thành từng loại
  const suppliesToCreate = dataInFile.filter(item => {
    return (
      !mapSuppliesCode[item.ma_vat_tu]
      && !!item.ma_vat_tu
      && !!mapSuppliesCategory[item.id_danh_diem]
      && !!mapLicense[item.so_giay_phep]
      && !!mapPoweredBy[item.id_nguon_cap]
      && !!mapCalUnit[item.ma_don_vi_tinh]
      && !mapSerial[item.serial]
      && (new Date(item.ngay_cap) <= new Date(item.han_su_dung))
    );
  });
  const suppliesToUpdate = dataInFile.filter(item => {
    return (
      !!mapSuppliesCode[item.ma_vat_tu]
      && !!item.ma_vat_tu
      && !!mapSuppliesCategory[item.id_danh_diem]
      && !!mapLicense[item.so_giay_phep]
      && !!mapPoweredBy[item.id_nguon_cap]
      && !!mapCalUnit[item.ma_don_vi_tinh]
      && !mapSerial[item.serial]
      && (new Date(item.ngay_cap) <= new Date(item.han_su_dung))
    );
  });
  const failsRecord = dataInFile.filter(item => {
    return (
      !mapSuppliesCategory[item.id_danh_diem]
      || !item.ma_vat_tu
      || !mapLicense[item.so_giay_phep]
      || !mapPoweredBy[item.id_nguon_cap]
      || !mapCalUnit[item.ma_don_vi_tinh]
      || !!mapSerial[item.serial]
      || !(new Date(item.ngay_cap) <= new Date(item.han_su_dung))
    );
  });

  let docs = []; //Các bản ghi được trả về client
  //Create
  for (const supplies of suppliesToCreate) {
    let value = supplies;
    value.id_danh_diem = mapSuppliesCategory[value.id_danh_diem];
    value.so_giay_phep = mapLicense[value.so_giay_phep];
    value.id_nguon_cap = mapPoweredBy[value.id_nguon_cap];
    value.ma_don_vi_tinh = mapCalUnit[value.ma_don_vi_tinh];
  }
  let newSupplies = await Model.create(suppliesToCreate);
  newSupplies = await Model.populate(newSupplies, [
    { path: 'id_danh_diem', select: 'ten_danh_diem quan_ly_theo_serial' },
    { path: 'ma_don_vi_tinh', select: 'ten_don_vi_tinh' },
    { path: 'id_nguon_cap', select: 'ten_nguon_cap' },
    { path: 'so_giay_phep', select: 'so_giay_phep' },
  ]);


  // Update
  for (const supplies of suppliesToUpdate) {
    let value = supplies;
    value.id_danh_diem = mapSuppliesCategory[value.id_danh_diem];
    value.so_giay_phep = mapLicense[value.so_giay_phep];
    value.id_nguon_cap = mapPoweredBy[value.id_nguon_cap];
    value.ma_don_vi_tinh = mapCalUnit[value.ma_don_vi_tinh];
  }
  await Model.bulkWrite(
    suppliesToUpdate.map((row) =>
      ({
        updateOne: {
          filter: { ma_vat_tu: row.ma_vat_tu },
          update: { $set: row },
          upsert: false,
        },
      }),
    ),
  );
  const updateSuppliesCode = suppliesToUpdate.map(row => row.ma_vat_tu);
  let updateSupplies = await Model.find({ ma_vat_tu: { $in: updateSuppliesCode } })
    .populate({ path: 'id_danh_diem', select: 'ten_danh_diem quan_ly_theo_serial' })
    .populate({ path: 'ma_don_vi_tinh', select: 'ten_don_vi_tinh' })
    .populate({ path: 'so_giay_phep', select: 'so_giay_phep' })
    .populate({ path: 'id_nguon_cap', select: 'ten_nguon_cap' })
    .lean();
  let fails = [];
  for (const item of failsRecord) {
    item.type_err = '';
    if (!item.ma_vat_tu) {
      item.type_err = 'Mã vật tư không đúng định dạng!\n';
    }
    if (!mapLicense[item.so_giay_phep]) {
      item.type_err += 'Số giấy phép không xác định!\n';
    }
    if (!mapSuppliesCategory[item.id_danh_diem]) {
      item.type_err += 'Danh điểm vật tư không xác định!\n';
    }
    if (!mapPoweredBy[item.id_nguon_cap]) {
      item.type_err += 'Nguồn cấp không xác định!\n';
    }
    if (!mapCalUnit[item.ma_don_vi_tinh]) {
      item.type_err += 'Đơn vị tính không xác định!\n';
    }
    if (!!mapSerial[item.serial]) {
      item.type_err += 'Serial đã tồn tại!\n';
    }
    if (!(new Date(item.ngay_cap) <= new Date(item.han_su_dung))) {
      item.type_err += 'Ngày cấp lớn hơn ngày hết hạn!\n';
    }
    fails = [...fails, item];
  }

  if (newSupplies) {
    docs = [...docs, newSupplies];
  }
  if (updateSupplies) {
    docs = [...docs, updateSupplies];
  }
  return responseAction.success(res, { docs, fails, success: true });
}
