import express from 'express';
import passport from 'passport';
import * as suachuachitietController from './suachuavattuchitiet.controller';

export const suachuavattuchitietRouter = express.Router();
suachuavattuchitietRouter
  .route('/')
  .get(passport.authenticate('jwt', { session: false }), suachuachitietController.getAll)
  .post(passport.authenticate('jwt', { session: false }), suachuachitietController.create)

suachuavattuchitietRouter
  .route('/:id')
  .get(passport.authenticate('jwt', { session: false }), suachuachitietController.findOne)
  .delete(passport.authenticate('jwt', { session: false }), suachuachitietController.remove)
  .put(passport.authenticate('jwt', { session: false }), suachuachitietController.update);
