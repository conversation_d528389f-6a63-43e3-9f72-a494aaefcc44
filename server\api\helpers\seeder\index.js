import User from '../../resources/user/user.model';
import UserDefault from './initdata/UserDefault';
import RoleDefault from './initdata/RoleDefault';
import ToChucDefault from './initdata/ToChucDefault';
import Role from '../../resources/role/role.model';
import ToChucQuanLy from '../../resources/tochucquanly/donVi.model';

async function initData() {

  async function initRole() {
    const count = await Role.countDocuments({ code: RoleDefault.code });
    if (count) return;
    await Role.create(RoleDefault);
  }

  await initRole();

  async function initToChuc() {
    const count = await ToChucQuanLy.countDocuments({ ma_don_vi: ToChucDefault.ma_don_vi });
    if (count) return;
    await ToChucQuanLy.create(ToChucDefault);
  }

  await initToChuc();

  async function initSystemAdmin() {
    const systemRole = await Role.findOne({ code: RoleDefault.code });
    const donVi = await ToChucQuanLy.findOne({ ma_don_vi: ToChucDefault.ma_don_vi });
    const count = await User.countDocuments({ is_system_admin: true });
    if (count) return;
    await User.create({ ...UserDefault, role_id: [systemRole._id], id_don_vi: donVi._id });
  }

  await initSystemAdmin();
  console.log('Init data done');
}

export default async function() {

  try {
    await initData();
  } catch (e) {
    console.log(e);
  }
};
