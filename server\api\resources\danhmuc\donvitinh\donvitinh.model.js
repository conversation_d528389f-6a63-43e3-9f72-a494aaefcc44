import mongoose, { Schema } from 'mongoose';
import { DONVITINH } from '../../../constant/dbCollections';
import mongoosePaginate from 'mongoose-paginate-v2';

const schema = new Schema({
  ten_don_vi_tinh: { type: String, required: true, validate: /\S+/ },
  ma_don_vi_tinh: { type: String, required: true, validate: /\S+/, unique:true },
  isActive: { type: Boolean, default: true },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  versionKey: false,
});
schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(DONVITINH, schema);
