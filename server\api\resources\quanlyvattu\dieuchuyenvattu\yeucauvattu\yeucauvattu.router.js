import express from 'express';
import passport from 'passport';
import * as yeuCauVattuController from './yeucauvattu.controller';
import { authorizationMiddleware } from '../../../rbac/middleware';
import ManagePermission from '../../../rbac/permissions/LichSuVatTuPermission';
import { router } from '../../lichsu/router';
import QuanLyVatTuPermisson from '../../../rbac/permissions/quanlyvattu/QuanLyVatTuPermisson';

export const yeucauvattuRouter = express.Router();
yeucauvattuRouter.use(passport.authenticate('jwt', { session: false }));

yeucauvattuRouter.post('*', authorizationMiddleware([QuanLyVatTuPermisson.CREATE ]));
yeucauvattuRouter.get('*', authorizationMiddleware([QuanLyVatTuPermisson.READ]));
yeucauvattuRouter.put('/:id', authorizationMiddleware([QuanLyVatTuPermisson.UPDATE ]));
yeucauvattuRouter.put('/duyet/:id', authorizationMiddleware([QuanLyVatTuPermisson.CONFIRM ]));
yeucauvattuRouter.delete('*', authorizationMiddleware([QuanLyVatTuPermisson.DELETE]));

yeucauvattuRouter
  .route('/')
  .get(yeuCauVattuController.getAll)
  .post(yeuCauVattuController.create);
yeucauvattuRouter
  .route('/duyet/:id')
  .put(yeuCauVattuController.update)

yeucauvattuRouter
  .route('/:id')
  .get(yeuCauVattuController.findOne)
  .delete(yeuCauVattuController.remove)
  .put(yeuCauVattuController.update);
