import mongoose, { Schema } from 'mongoose';
import { DIEUCHUYENVATTU, DONVI, NHANVIEN, TOCHUC_QUANLY, YEUCAUVATTU } from '../../../constant/dbCollections';
import mongoosePaginate from 'mongoose-paginate-v2';
import { LOAI_DIEU_CHUYEN, TRANG_THAI } from '../../../constant/constant';

const schema = new Schema({
  id_donvi_di: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: TOCHUC_QUANLY,
  },
  id_nguoi_di: {
    type: mongoose.Schema.Types.ObjectId,
    ref: NHANVIEN,
  },
  id_nguoi_den: {
    type: mongoose.Schema.Types.ObjectId,
    ref: NHANVIEN,
  },
  id_donvi_den: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: TOCHUC_QUANLY,
  },
  thoi_gian_yeu_cau: { type: Date, required: true },
  ly_do: { type: String },
  thoi_gian_duyet: { type: Date },
  dia_diem_ban_giao: { type: String },
  thoi_gian_thuc_hien: { type: Date },
  thoi_gian_hoan_thanh: { type: Date },
  can_cu: { type: String },
  trang_thai: {
    type: String,
    enum: Object.values(TRANG_THAI),
    default: TRANG_THAI.DANG_YEU_CAU,
  },
  phan_loai: {
    type: String,
    enum: Object.values(LOAI_DIEU_CHUYEN),
    default: LOAI_DIEU_CHUYEN.DIEU_CHUYEN,
  },
  isActive: { type: Boolean, default: true },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  versionKey: false,
});
schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(DIEUCHUYENVATTU, schema);
