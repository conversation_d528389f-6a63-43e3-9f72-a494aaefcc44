import * as ValidatorHelper from '../../../helpers/validatorHelper';
import CAPNHATTINHTRANG from './capnhattinhtrangvattu.model';
const Joi = require('joi');

const objSchema = Joi.object({
  id_nguoi1: Joi.string().required().messages(ValidatorHelper.messageDefine('Người tiến hành')),
  id_donvi: Joi.string().required().messages(ValidatorHelper.messageDefine('Đơn vị')),
  trang_thai: Joi.string().messages(ValidatorHelper.messageDefine('<PERSON><PERSON><PERSON> thành')),
  thoi_gian_thuc_hien: Joi.date().required().messages(ValidatorHelper.messageDefine('<PERSON><PERSON><PERSON> thanh lý')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}


export async function getAll(query) {
  return CAPNHATTINHTRANG.find(query).lean();
}
