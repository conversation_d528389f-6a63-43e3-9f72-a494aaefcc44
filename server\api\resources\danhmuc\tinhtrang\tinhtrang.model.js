import mongoose, { Schema } from 'mongoose';
import { TINHTRANG } from '../../../constant/dbCollections';
import mongoosePaginate from 'mongoose-paginate-v2';

const schema = new Schema({
  ten_tinh_trang: { type: String, required: true, validate: /\S+/ },
  ma_tinh_trang: { type: String, required: true, unique: true, index: true },
  mo_ta_tinh_trang: { type: String },
  isActive: { type: Boolean, default: true },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  versionKey: false,
});
schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(TINHTRANG, schema);
