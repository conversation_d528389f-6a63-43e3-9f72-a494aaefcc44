import mongoose, { Schema } from 'mongoose';
import { TIEP_NHAN, TIEP_NHAN_CHITIET, TINHTRANG, VATTU } from '../../../../constant/dbCollections';
import mongoosePaginate from 'mongoose-paginate-v2';

const schema = new Schema({
  id_tiepnhan: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: TIEP_NHAN,
  },
  id_vattu: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: VATTU,
  },
  id_tinhtrang: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: TINHTRANG,
  },
  ghichu: { type: String },
  soluong: { type: Number, required: true },
  isActive: { type: Boolean, default: true },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  versionKey: false,
});
schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(TIEP_NHAN_CHITIET, schema);
