import mongoose, { Schema } from 'mongoose';
import { DIEUCHUYENVATTU, DIEUCHUYENVATTU_CHITIET, KHOVATTU, TINHTRANG, VATTU } from '../../../../constant/dbCollections';
import mongoosePaginate from 'mongoose-paginate-v2';

const schema = new Schema({
  id_dieu_chuyen: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: DIEUCHUYENVATTU,
  },
  id_vattu: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: VATTU,
  },
  id_kho: {
    type: mongoose.Schema.Types.ObjectId,
    ref: KHOVATTU,
  },
  id_tinhtrang: {
    type: mongoose.Schema.Types.ObjectId,
    ref: TINHTRANG,
    // required: true,
  },
  ghichu: { type: String },
  soluong: { type: Number, required: true },
  isActive: { type: Boolean, default: true },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  versionKey: false,
});
schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(DIEUCHUYENVATTU_CHITIET, schema);
