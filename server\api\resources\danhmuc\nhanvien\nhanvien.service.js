import * as ValidatorHelper from '../../../helpers/validatorHelper';

const Joi = require('joi');


const objSchema = Joi.object({
  ten_nhan_vien: Joi.string().required().messages(ValidatorHelper.messageDefine('Tên nhân viên')),
  cmnd: Joi.string().required().messages(ValidatorHelper.messageDefine('Số chứng minh thư')),
  sdt: Joi.string().required().messages(ValidatorHelper.messageDefine('Số điện thoại')),
  ngay_cap: Joi.date().required().messages(ValidatorHelper.messageDefine('<PERSON><PERSON><PERSON> cấp')),
  noi_cap: Joi.string().required().messages(ValidatorHelper.messageDefine('Nơi cấp')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}
