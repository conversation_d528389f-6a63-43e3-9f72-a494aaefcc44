import express from 'express';
import passport from 'passport';
import * as Controller from './controller';
import { authorizationMiddleware } from '../../rbac/middleware';

import QuanLyVatTuPermisson from '../../rbac/permissions/quanlyvattu/QuanLyVatTuPermisson';

export const router = express.Router();
router.use(passport.authenticate('jwt', { session: false }));

router.post('*', authorizationMiddleware([QuanLyVatTuPermisson.CREATE]));
router.get('*', authorizationMiddleware([QuanLyVatTuPermisson.READ]));
router.put('/:id', authorizationMiddleware([QuanLyVatTuPermisson.UPDATE]));
router.put('/duyet/:id', authorizationMiddleware([QuanLyVatTuPermisson.CONFIRM]));
router.delete('*', authorizationMiddleware([QuanLyVatTuPermisson.DELETE]));

router
  .route('/')
  .get(Controller.getAll)
  .post(Controller.create);

router
  .route('/duyet/:id')
  .put(Controller.update)

router
  .route('/:id')
  .get(Controller.findOne)
  .delete(Controller.remove)
  .put(Controller.update);
