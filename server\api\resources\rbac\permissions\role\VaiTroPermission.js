import {create} from "../../permissionHelper";
import resources from "../../Resources";
import actions from "../../Actions";

export default {
  CREATE: create(resources.ROLE, actions.CREATE),
  UPDATE: create(resources.ROLE, actions.UPDATE),
  CONFIRM: create(resources.ROLE, actions.CONFIRM),
  SELECT: create(resources.ROLE, actions.SELECT),
  READ: create(resources.ROLE, actions.READ),
  DELETE: create(resources.ROLE, actions.DELETE)
}
