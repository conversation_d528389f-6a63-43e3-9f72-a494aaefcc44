import express from 'express';
import passport from 'passport';
import * as vattuController from './vattu.controller';
import { checkTempFolder, multipartMiddleware } from '../../../utils/fileUtils';
import { authorizationMiddleware } from '../../rbac/middleware';
import DanhMucPermission from '../../rbac/permissions/danhmuc/DanhMucPermission';

export const vattuRouter = express.Router();

vattuRouter.use(passport.authenticate('jwt', { session: false }));
vattuRouter.get('*', authorizationMiddleware([DanhMucPermission.READ]));
vattuRouter.post('*', authorizationMiddleware([DanhMucPermission.CREATE]));
vattuRouter.put('*', authorizationMiddleware([DanhMucPermission.UPDATE]));
vattuRouter.delete('*', authorizationMiddleware([DanhMucPermission.DELETE]));

vattuRouter
  .route('/used')
  .get( vattuController.getAllUsed)
vattuRouter
  .route('/')
  .get( vattuController.getAll)
  .post( vattuController.create);

vattuRouter
  .route('/:id')
  .get( vattuController.findOne)
  .delete( vattuController.remove)
  .put( vattuController.update);

