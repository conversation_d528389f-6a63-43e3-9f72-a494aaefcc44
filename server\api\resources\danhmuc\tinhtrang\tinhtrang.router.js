import express from 'express';
import passport from 'passport';
import * as tinhtrangController from './tinhtrang.controller';
import { authorizationMiddleware } from '../../rbac/middleware';
import DanhMucPermission from '../../rbac/permissions/danhmuc/DanhMucPermission';

export const tinhtrangRouter = express.Router();

tinhtrangRouter.use(passport.authenticate('jwt', { session: false }));
tinhtrangRouter.get('*', authorizationMiddleware([DanhMucPermission.READ]));
tinhtrangRouter.post('*', authorizationMiddleware([DanhMucPermission.CREATE]));
tinhtrangRouter.put('*', authorizationMiddleware([DanhMucPermission.UPDATE]));
tinhtrangRouter.delete('*', authorizationMiddleware([DanhMucPermission.DELETE]));
tinhtrangRouter
  .route('/')
  .get(tinhtrangController.getAll)
  .post(tinhtrangController.create);

tinhtrangRouter
  .route('/:id')
  .get(tinhtrangController.findOne)
  .delete(tinhtrangController.remove)
  .put(tinhtrangController.update);
