import * as ValidatorHelper from '../../../helpers/validatorHelper';
import VatTuDauKy from './vattudauky.model';
import * as ToChucQuanLyService from '../../tochucquanly/donVi.service';
import * as DanhDiemService from '../../danhmuc/danhdiemvattu/danhdiem.service';
import * as VatTuService from '../../danhmuc/vattu/vattu.service';
import * as TinhTrangService from '../../danhmuc/tinhtrang/tinhtrang.service';
import * as NguonCapService from '../../danhmuc/nguoncap/nguoncap.service';
import { checkDate, checkNumber, convertDate } from '../../../helpers/checkDataHelper';
import VatTuModel from '../../danhmuc/vattu/vattu.model';
import * as responseHelper from '../../../helpers/responseHelper';

export async function getAll(query) {
  return VatTuDauKy.find(query).lean();
}

export async function getAllChiTiet(query, populate = {
  path: 'id_don_vi id_tinh_trang id_vattu id_danh_diem',
  populate: { path: 'ma_don_vi_tinh' },
}) {
  return VatTuDauKy.find(query)
    .populate(populate).lean();
}

export async function getForTonKho(query) {
  return VatTuDauKy.find(query)
    .populate({ path: 'id_vattu' }).lean();
}

export async function getForTonKhoNew(query) {
  return VatTuDauKy.find(query)
    .sort('thoigian_dauky')
    .lean();
}

const Joi = require('joi');

const objSchema = Joi.object({
  id_vattu: Joi.string().required().messages(ValidatorHelper.messageDefine('Vật tư')),
  id_don_vi: Joi.string().required().messages(ValidatorHelper.messageDefine('Đơn vị')),
  id_kho: Joi.string().messages(ValidatorHelper.messageDefine('Kho')),
  id_tinh_trang: Joi.string().required().messages(ValidatorHelper.messageDefine('Tình trạng')),
  thoigian_dauky: Joi.date().required().messages(ValidatorHelper.messageDefine('Thời gian đầu kỳ')),
  soluong: Joi.number().required().messages(ValidatorHelper.messageDefine('Số lượng đầu kỳ')),
  ghichu: Joi.string().required().messages(ValidatorHelper.messageDefine('Ghi chú')),
});

export function validate(data, method) {
  let schema = ValidatorHelper.createValidatorSchema(objSchema, data, method);
  const { value, error } = schema.validate(data, { allowUnknown: true, abortEarly: true });
  if (error && error.details) {
    return { error };
  }
  return { value };
}

const Headers = {
  DON_VI: 'Đơn vị',
  TEN_VAT_TU: 'Tên VK, VLN, CCHT',
  SERIAL: 'Số hiệu, ký hiệu',
  SO_LO: 'Số lô',
  THOI_GIAN_DAU_KY: 'Thời gian đầu kỳ',
  SO_GIAY_PHEP: 'Số giấy phép',
  SO_LUONG: 'Số lượng',
  TINH_TRANG_KY_THUAT: 'Tình trạng kỹ thuật',
  NGUON_CAP: 'Nguồn cấp',
  NGAY_CAP: 'Ngày cấp',
  HAN_SU_DUNG: 'Hạn sử dụng',
  NUOC_SAN_XUAT: 'Nước sản xuất',
  DONG_BO_KEM_THEO: 'Đồng bộ kèm theo',
  GHI_CHU: 'Ghi chú',
};

export async function mapData(mapDonVi, mapTinhTrang, mapNguonCap, mapDanhDiem, mapVatTu) {
  const allDonVi = await ToChucQuanLyService.getAll({ is_deleted: false });
  allDonVi.forEach(donVi => {
    mapDonVi[donVi.ten_don_vi] = donVi._id;
  });

  const allTinhTrang = await TinhTrangService.getAll({ is_deleted: false });
  allTinhTrang.forEach(tinhTrang => {
    mapTinhTrang[tinhTrang.ten_tinh_trang] = tinhTrang._id;
  });

  const allNguonCap = await NguonCapService.getAll({ is_deleted: false });
  allNguonCap.forEach(nguonCap => {
    mapNguonCap[nguonCap.ten_nguon_cap] = nguonCap._id;
  });

  const allDanhDiem = await DanhDiemService.getAll({ is_deleted: false });
  allDanhDiem.forEach(danhDiem => {
    mapDanhDiem[danhDiem.ten_danh_diem] = danhDiem._id;
  });

  const allVatTu = await VatTuService.getAll({ is_deleted: false });
  allVatTu.forEach(vatTu => {
    mapVatTu[vatTu.serial + vatTu.id_danh_diem] = vatTu._id;
    // mapVatTu[vatTu.serial] = vatTu._id;
  });
}

function trimData(dataInput) {
  if (!Array.isArray(dataInput?.rows)) return dataInput;
  dataInput.rows.forEach(row => {
    Object.entries(row).forEach(([key, value]) => {
      row[key] = (!!value && typeof value === 'string') ? value.trim() : value;
    });
  });
  return dataInput;
}

export async function importData(sheetData) {
  trimData(sheetData);

  const mapDonVi = {}, mapTinhTrang = {}, mapNguonCap = {}, mapVatTu = {}, mapDanhDiem = {};

  await mapData(mapDonVi, mapTinhTrang, mapNguonCap, mapDanhDiem, mapVatTu); //Map name to _id

  //Convert dữ liệu trong sheet thành dữ liệu để create/update vật tư
  function convertToDBSupplies(row) {
    return {
      id_danh_diem: mapDanhDiem[row[Headers.TEN_VAT_TU]?.trim()],
      serial: row[Headers.SERIAL],
      so_lo: row[Headers.SO_LO],
      so_giay_phep: row[Headers.SO_GIAY_PHEP],
      ngay_cap: convertDate(row[Headers.NGAY_CAP]),
      han_su_dung: convertDate(row[Headers.HAN_SU_DUNG]),
      nuoc_san_xuat: row[Headers.NUOC_SAN_XUAT],
      dong_bo_kem_theo: row[Headers.DONG_BO_KEM_THEO],
      ghi_chu: row[Headers.GHI_CHU],
      id_nguon_cap: mapNguonCap[row[Headers.NGUON_CAP]],
      is_deleted: false,
    };
  }

  //Nếu chưa có vật tư thì thêm mới vật tư theo serial
  const dataToSupplies = sheetData?.map(row => convertToDBSupplies(row)).filter(element => !mapVatTu[element.serial]);
  let newSupplies = await VatTuModel.create(dataToSupplies);

  const serialSupplies = newSupplies.map(row => row.serial);
  let updateSupplies = await VatTuModel.find({ serial: { $in: serialSupplies } })
    .populate({ path: 'id_danh_diem', populate: { path: 'ma_don_vi_tinh' } })
    .populate({ path: 'id_nguon_cap', select: 'ten_nguon_cap' })
    .lean();

  //Map serial to _id để thêm mới vật tư đầu kỳ
  updateSupplies.forEach(supply => {
    mapVatTu[supply.serial] = supply._id;
  });

  //Convert sheetdata to row để thêm mới vật tư đầu kỳ
  function convertToDBImport(row) {
    return {
      id_vattu: mapVatTu[row[Headers.SERIAL]],
      id_tinh_trang: mapTinhTrang[row[Headers.TINH_TRANG_KY_THUAT]?.trim()],
      id_danh_diem: mapDanhDiem[row[Headers.TEN_VAT_TU]?.trim()],
      id_don_vi: mapDonVi[row[Headers.DON_VI]?.trim()],
      so_luong: row[Headers.SO_LUONG],
      thoigian_dauky: convertDate(row[Headers.THOI_GIAN_DAU_KY]),
      is_deleted: false,
    };
  }

  const dataToImport = sheetData?.map(row => convertToDBImport(row)).filter(element => element.id_vattu);
  return await VatTuDauKy.bulkWrite(
    dataToImport.map((element) =>
      ({
        updateOne: {
          filter: { id_vattu: element.id_vattu },
          update: { $set: element },
          upsert: true,
        },
      }),
    ),
  );
}

export async function checkImport(sheetData) {
  trimData(sheetData);
  if (!sheetData) return null;
  const { rows } = sheetData;
  const mapDonVi = {}, mapTinhTrang = {}, mapNguonCap = {}, mapVatTu = {}, mapDanhDiem = {};
  await mapData(mapDonVi, mapTinhTrang, mapNguonCap, mapDanhDiem, mapVatTu);

  function createError(col, error) {
    return { col, error };
  }

  function validateRow(row) {
    let errors = [];

    if (!row[Headers.DON_VI]?.trim()) {
      errors = [...errors, createError(Headers.DON_VI, 'Thiếu đơn vị')];
    } else {
      if (!mapDonVi[row[Headers.DON_VI]?.trim()]) {
        errors = [...errors, createError(Headers.DON_VI, 'Đơn vị quản lý không đúng hoặc chưa được tạo')];
      }
    }

    if (!row[Headers.TEN_VAT_TU]?.trim()) {
      errors = [...errors, createError(Headers.TEN_VAT_TU, 'Thiếu tên VK, VLN, CCHT')];
    } else {
      if (!mapDanhDiem[row[Headers.TEN_VAT_TU]?.trim()]) {
        errors = [...errors, createError(Headers.TEN_VAT_TU, 'Tên VK, VLN, CCHT không đúng hoặc chưa được tạo')];
      }
    }

    if (!row[Headers.SERIAL]) {
      errors = [...errors, createError(Headers.SERIAL, 'Thiếu số hiệu, ký hiệu')];
    } else {
      if (mapVatTu[row[Headers.SERIAL] + mapDanhDiem[row[Headers.TEN_VAT_TU]]]) {
        errors = [...errors, createError(Headers.SERIAL, `Vật tư có Số hiệu ký hiệu ${row[Headers.SERIAL]} đã được nhập`)];
      }
    }
    // if (!row[Headers.NGUON_CAP]?.trim()) {
    //   errors = [...errors, createError(Headers.NGUON_CAP, 'Thiếu nguồn cấp')];
    // } else {
    if (row[Headers.NGUON_CAP]?.trim() && !mapNguonCap[row[Headers.NGUON_CAP]?.trim()]) {
      errors = [...errors, createError(Headers.NGUON_CAP, 'Nguồn cấp chưa đúng hoặc chưa được tạo')];
    }
    // }

    if (!row[Headers.TINH_TRANG_KY_THUAT]?.trim()) {
      errors = [...errors, createError(Headers.TINH_TRANG_KY_THUAT, 'Thiếu tình trạng kỹ thuật')];
    } else {
      if (row[Headers.TINH_TRANG_KY_THUAT]?.trim() && !mapTinhTrang[row[Headers.TINH_TRANG_KY_THUAT]?.trim()]) {
        errors = [...errors, createError(Headers.TINH_TRANG_KY_THUAT, 'Tình trạng kỹ thuật không đúng hoặc chưa được tạo')];
      }
    }

    if (!row[Headers.SO_LUONG] || !checkNumber(row[Headers.SO_LUONG])) {
      errors = [...errors, createError(Headers.SO_LUONG, 'Không đúng hoặc không có')];
    }
    if (!checkDate(row[Headers.NGAY_CAP])) {
      errors = [...errors, createError(Headers.NGAY_CAP, 'Ngày vận hành không đúng, định dạng đúng là DD/MM/YYYY')];
    }

    if (!checkDate(row[Headers.HAN_SU_DUNG])) {
      errors = [...errors, createError(Headers.HAN_SU_DUNG, 'Hạn sử dụng không đúng, định dạng đúng là DD/MM/YYYY')];
    }
    if (!checkDate(row[Headers.THOI_GIAN_DAU_KY])) {
      errors = [...errors, createError(Headers.THOI_GIAN_DAU_KY, 'Thời gian đầu kỳ không đúng hoặc không có')];
    }

    if (!checkNumber(row[Headers.STT])) {
      errors = [...errors, createError(Headers.STT, 'Không đúng hoặc không có')];
    }
    if (errors.length) {
      row['Lỗi'] = errors;
    } else {
      row['Lỗi'] = null;
    }
    return row;
  }

  sheetData.rows = rows.map(row => validateRow(row));
  return sheetData;
}

export async function  getDataToCreateManySupplies(res, dataCreate) {
  if (!dataCreate) return null;
  let newSupplies = await VatTuService.checkUniqueSupply(res, dataCreate) || [];
  const mapSupplies = {};
  newSupplies?.forEach(item => {
    mapSupplies[item.serial] = item._id;
  });

  function convertDataToRow(supply) {
    return {
      id_danh_diem: supply.id_danh_diem,
      id_vattu: mapSupplies[supply.serial],
      id_don_vi: supply.id_don_vi,
      id_tinh_trang: supply.id_tinh_trang,
      id_nguon_cap: supply.id_nguon_cap,
      thoigian_dauky: supply.thoigian_dauky,
      ghi_chu: supply.ghi_chu,
      so_luong: supply.so_luong,
    };
  }

  return dataCreate.map(convertDataToRow);
}

