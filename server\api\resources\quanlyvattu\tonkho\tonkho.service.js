import * as VatTuService from '../../danhmuc/vattu/vattu.service';
import * as ToChucService from '../../tochucquanly/donVi.service';
import * as BangTheoDoiService from './bangtheodoi.service';
import { bangTongHopThucLuc } from './bangtheodoi.service';
import DANHDIEMVATTU from '../../danhmuc/danhdiemvattu/danhdiem.model';
import DONVITINH from '../../danhmuc/donvitinh/donvitinh.model';
import TINHTRANG from '../../danhmuc/tinhtrang/tinhtrang.model';
import Joi from 'joi';
import VATTU from '../../danhmuc/vattu/vattu.model';
import { groupBy } from '../../../common/functionCommons';
import LOAIVATTU from '../../danhmuc/loaivattu/loaivattu.model';

function createGroupKey(...keys) {
  return keys.join();
}

export async function tonkhoTheoDanhDiem(tgDauky, tgCuoiky, idDonvi, danhDiem) {
  try {
    let result = await BangTheoDoiService.bangTongHopThucLucDanhDiem(tgDauky, tgCuoiky, idDonvi, danhDiem);
    const yeuCauDaDuyet = await BangTheoDoiService.bangYeuCauDaDuyet(tgDauky, tgCuoiky, idDonvi, danhDiem);
    yeuCauDaDuyet.forEach(element => {
      const groupKey = createGroupKey(element.id_don_vi, element.id_danh_diem);
      const current = result[groupKey];
      if (current) {
        current.soluong_giam_dukien = current.soluong_giam_dukien + element.soluong_giam_dukien;
        current.soluong_tang_dukien = current.soluong_tang_dukien + element.soluong_tang_dukien;
        current.tonkho_cuoiky_dukien = current.tonkho_cuoiky + current.soluong_tang_dukien - current.soluong_giam_dukien;
      }
    });
    result = Object.values(result);
    result = await DANHDIEMVATTU.populate(result, 'id_vattu.id_danh_diem');
    result = await DANHDIEMVATTU.populate(result, 'id_danh_diem');
    return await DONVITINH.populate(result, 'id_danh_diem.ma_don_vi_tinh');

  } catch (e) {
    console.log(e);
    return null;
  }
}

export async function tonkhoTheoVattu(tgBatDau, tgKetThuc, idDonvi, idVattu, idDanhDiem, serial) {
  function nhomTinhTrangVattu(filltered) {
    const tonKho = {};
    filltered.forEach(element => {
      const groupKey = createGroupKey(element.id_don_vi, element.id_vattu);
      const banGhiHienTai = tonKho[groupKey];
      if (!banGhiHienTai) {
        tonKho[groupKey] = {
          ...element,
          tinhtrang: [],
        };
      } else {
        tonKho[groupKey] = {
          ...banGhiHienTai,
          tonkho_dauky: banGhiHienTai.tonkho_dauky + element.tonkho_dauky,
          soluong_tang: banGhiHienTai.soluong_tang + element.soluong_tang,
          soluong_tang_dukien: banGhiHienTai.soluong_tang_dukien + element.soluong_tang_dukien,
          soluong_giam: banGhiHienTai.soluong_giam + element.soluong_giam,
          soluong_giam_dukien: banGhiHienTai.soluong_giam_dukien + element.soluong_giam_dukien,
          tonkho_cuoiky: banGhiHienTai.tonkho_cuoiky + element.tonkho_cuoiky,
          tonkho_cuoiky_dukien: banGhiHienTai.tonkho_cuoiky_dukien + element.tonkho_cuoiky_dukien,
        };
      }
      tonKho[groupKey].id = element.id_vattu;
      tonKho[groupKey].tinhtrang = [...tonKho[groupKey].tinhtrang, element];
    });
    return Object.values(tonKho);
  }

  try {
    let vattuIds = undefined;
    if (idVattu) {
      const vattu = await VatTuService.getAll({ _id: idVattu });
      vattuIds = vattu.map((element) => element._id);
    }
    let query = {};
    if (idDanhDiem) {
      query.id_danh_diem = idDanhDiem;
    }
    if (serial) {
      query.serial = serial;
    }
    if (query) {
      const vattu = await VatTuService.getAll(query);
      vattuIds = vattu.map((element) => element._id);
    }
    let result = await BangTheoDoiService.bangTongHopThucLuc(tgBatDau, tgKetThuc, idDonvi, vattuIds);
    result = Object.values(result);
    if (idDonvi) {
      result = result.filter(element => element.id_don_vi.toString() === idDonvi);
    }
    result = nhomTinhTrangVattu(result);
    result = await VATTU.populate(result, 'id_vattu');
    result = await TINHTRANG.populate(result, 'tinhtrang.id_tinh_trang');
    result = await DANHDIEMVATTU.populate(result, 'id_vattu.id_danh_diem');
    result = await DONVITINH.populate(result, 'id_vattu.id_danh_diem.ma_don_vi_tinh');
    result = await LOAIVATTU.populate(result, 'id_vattu.id_danh_diem.loai_vat_tu_id');
    return optimizeData(result, tonKhoTheoMaVattuSchema);
  } catch (e) {
    console.log(e);
    return null;
  }
}

const tonKhoTheoMaVattuSchema = Joi.array().items(Joi.object({
  id: Joi.string(),
  id_don_vi: Joi.string(),
  id_vattu: Joi.object({
    id_danh_diem: Joi.object({
      _id: Joi.string(),
      ten_danh_diem: Joi.string(),
      ma_don_vi_tinh: Joi.object({
        _id: Joi.string(),
        ten_don_vi_tinh: Joi.string(),
      }).options({ stripUnknown: true }),
    }).options({ stripUnknown: true }),
  }).options({ stripUnknown: true }),
  soluong_giam: Joi.number(),
  soluong_tang: Joi.number(),
  thoigian_dauky: Joi.date(),
  tonkho_dauky: Joi.number(),
  tonkho_cuoiky: Joi.number(),
  tinhtrang: Joi.array()
    .items(Joi.object().keys({
      id_tinh_trang: Joi.string(),
      tonkho_dauky: Joi.number(),
      tonkho_cuoiky: Joi.number(),
    }).options({ stripUnknown: true })),
}).options({ stripUnknown: true }));

function optimizeData(data, schema) {
  const { value } = schema.validate(data);
  return value;
}

export async function getKhongThoaManTonKhoVattu(idDonVi, chitietDieuChuyen) {
  const idVattus = chitietDieuChuyen.map(element => element.id_vattu);
  const tonKhoVattuHienTai = await bangTongHopThucLuc(undefined, new Date(), idDonVi, idVattus);
  return chitietDieuChuyen.filter(element => {
    const uniqueKey = createGroupKey(idDonVi, element.id_vattu, element.id_tinhtrang);
    const tonKhoHienTai = tonKhoVattuHienTai[uniqueKey];
    if (tonKhoHienTai) {
      return element.soluong > tonKhoHienTai.tonkho_cuoiky;
    }
    return false;
  });
}

export async function getKhongThoaManTonKhoDanhdiem(idDonVi, chitietYeuCau) {
  const danhDiem = chitietYeuCau.map(element => element.id_danhdiem);
  const danhDiemQuery = { $in: danhDiem };
  const tonKhoHienTai = await BangTheoDoiService.bangTongHopThucLucDanhDiem(undefined, new Date(), idDonVi, danhDiemQuery);
  return chitietYeuCau.filter(element => {
    const uniqueKey = createGroupKey(idDonVi, element.id_danhdiem);
    const elementHientai = tonKhoHienTai[uniqueKey];
    if (elementHientai) return element.soluong > (elementHientai.tonkho_cuoiky - elementHientai.soluong_giam_dukien);
    return false;
  });
}

export async function tonKhoTheoDonVi(tonKhoTheoDanhDiem, donViCha) {
  console.log(donViCha);
  const allDonVi = await ToChucService.getAll({
    $or: [{ don_vi_cha_id: donViCha }, { _id: donViCha }],
    is_deleted: false,
  });
  const allDonViNotPerentIds = allDonVi.filter(item => item?._id !== donViCha).map(item => item?._id);
  const allDonViChild = await ToChucService.getAll({
    $or: [{ don_vi_cha_id: { $in: allDonViNotPerentIds } }, { _id: donViCha }],
    is_deleted: false,
  });
  const allDonViIds = allDonViChild.map(item => item?._id.toString());

  tonKhoTheoDanhDiem.forEach(item => {
    item.id_danhdiem = item.id_danh_diem?._id;
  });
  const tonKho = tonKhoTheoDanhDiem.filter(item => allDonViIds.includes(item.id_don_vi?.toString()));

  const groupTonKhoByDonViIds = groupBy(tonKho, 'id_don_vi');
  const groupTonKhoByDanhDiemIds = groupBy(tonKho, 'id_danhdiem');

  const mapDanhDiem = {};
  tonKho.forEach(item => {
    if (!mapDanhDiem[item.id_danhdiem]) {
      mapDanhDiem[item.id_danhdiem] = {};
      mapDanhDiem[item.id_danhdiem].id_danh_diem = item.id_danh_diem;
      mapDanhDiem[item.id_danhdiem].tonkho_cuoiky = item.tonkho_cuoiky;
      mapDanhDiem[item.id_danhdiem].tonkho_dauky = item.tonkho_dauky;
      mapDanhDiem[item.id_danhdiem].soluong_giam = item.soluong_giam;
      mapDanhDiem[item.id_danhdiem].soluong_tang = item.soluong_tang;
    } else {
      mapDanhDiem[item.id_danhdiem].tonkho_cuoiky += item.tonkho_cuoiky;
      mapDanhDiem[item.id_danhdiem].tonkho_dauky += item.tonkho_dauky;
      mapDanhDiem[item.id_danhdiem].soluong_giam += item.soluong_giam;
      mapDanhDiem[item.id_danhdiem].soluong_tang += item.soluong_tang;
    }
  });
  let dataToanDonVi = [];
  Object.keys(groupTonKhoByDanhDiemIds).forEach((danhdiem, index) => {
    dataToanDonVi.push({ ...mapDanhDiem[danhdiem], ...{ stt: index + 1 } });
  });

  const mapDonVi = {};
  tonKho.forEach(item => {
    if (!mapDonVi[item?.id_don_vi]) {
      mapDonVi[item.id_don_vi] = {};
      mapDonVi[item.id_don_vi].ton_kho = groupTonKhoByDonViIds[item?.id_don_vi] ? groupTonKhoByDonViIds[item.id_don_vi] : [];
      mapDonVi[item.id_don_vi].tong_ton_kho = item.tonkho_cuoiky;
      mapDonVi[item.id_don_vi].tong_tang = item.soluong_tang;
      mapDonVi[item.id_don_vi].tong_giam = item.soluong_giam;
      mapDonVi[item.id_don_vi].tong_dau_ky = item.tonkho_dauky;
    } else {
      mapDonVi[item.id_don_vi].ton_kho = groupTonKhoByDonViIds[item.id_don_vi] ? groupTonKhoByDonViIds[item.id_don_vi] : [];
      mapDonVi[item.id_don_vi].tong_ton_kho += item.tonkho_cuoiky;
      mapDonVi[item.id_don_vi].tong_tang += item.soluong_tang;
      mapDonVi[item.id_don_vi].tong_giam += item.soluong_giam;
      mapDonVi[item.id_don_vi].tong_dau_ky += item.tonkho_dauky;
    }
  });
  let dataDonvi = [];
  allDonVi.forEach((donvi, index) => {
    donvi.stt = index + 1;
    if (mapDonVi[donvi?._id]) {
      dataDonvi.push({ ...mapDonVi[donvi?._id], ...donvi });
    } else {
      dataDonvi.push({
        ...{
          ton_kho: [],
          tong_ton_kho: 0,
          tong_tang: 0,
          tong_giam: 0,
          tong_dau_ky: 0,
        }, ...donvi,
      });
    }
  });

  return {
    data: dataToanDonVi,
    docs: dataDonvi,
  };
}
