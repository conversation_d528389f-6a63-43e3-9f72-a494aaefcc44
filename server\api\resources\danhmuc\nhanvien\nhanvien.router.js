import express from 'express';
import passport from 'passport';
import * as nhanvienController from './nhanvien.controller';
import { authorizationMiddleware } from '../../rbac/middleware';
import DanhMucPermission from '../../rbac/permissions/danhmuc/DanhMucPermission';

export const nhanvienRouter = express.Router();

nhanvienRouter.use(passport.authenticate('jwt', { session: false }));
nhanvienRouter.get('*', authorizationMiddleware([DanhMucPermission.READ]));
nhanvienRouter.post('*', authorizationMiddleware([DanhMucPermission.CREATE]));
nhanvienRouter.put('*', authorizationMiddleware([DanhMucPermission.UPDATE]));
nhanvienRouter.delete('*', authorizationMiddleware([DanhMucPermission.DELETE]));

nhanvienRouter.route('/')
  .get(nhanvienController.getAll)
  .post(nhanvienController.create);

nhanvienRouter
  .route('/:id')
  .get(nhanvienController.findOne)
  .delete(nhanvienController.remove)
  .put(nhanvienController.update);
