import * as responseAction from '../../../utils/responseAction';
import queryHelper from '../../../helpers/queryHelper';
import * as Service from './suachuavattu.service';
import Model from './suachuavattu.model';
import CommonError from '../../../error/CommonError';
import * as SuaChuaChiTietService from '../suachuavattuchitiet/suachuavattuchitiet.service';

export async function findOne(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findOne({ _id: id, is_deleted: false })
      .populate({ path: 'id_donvi', select: 'ten_don_vi' })
      .lean();
    if (!data) {
      return responseAction.error(res, CommonError.NOT_FOUND());
    }
    data.chitiet = await SuaChuaChiTietService.getAll({ id_suachua: data._id, is_deleted: false })
      .populate({ path: 'id_vattu', populate: { path: 'id_danh_diem' } })
      .populate({ path: 'id_tinhtrang' }).lean();
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function remove(req, res) {
  try {
    const { id } = req.params;
    const data = await Model.findOneAndUpdate({ _id: id }, { is_deleted: true }, { new: true }).lean();
    if (!data) {
      return responseAction.error(res, CommonError.NOT_FOUND());
    } else {
      await SuaChuaChiTietService.removeAll({ id_suachua: data._id });
      data.chitiet = await SuaChuaChiTietService.getAll({ id_suachua: data._id });
    }
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function update(req, res) {
  try {
    const { id } = req.params;
    const { error, value } = Service.validate(req.body);
    if (error) return responseAction.error(res, error, 400);
    const data = await Model.findByIdAndUpdate(id, value, { new: true }).lean();
    if (!data) {
      return responseAction.error(res, CommonError.NOT_FOUND());
    } else {
      const chitiet = req.body.chitiet || [];
      const chitietUpdate = chitiet.filter(row => row._id);
      await SuaChuaChiTietService.updateAll(chitietUpdate);
      let chitietCreate = chitiet.filter(row => !row.hasOwnProperty('_id'));
      chitietCreate.forEach(row => row.id_suachua = data._id);
      await SuaChuaChiTietService.create(chitietCreate);
      data.chitiet = await SuaChuaChiTietService.getAll({ id_suachua: data._id, is_deleted: false })
        .populate({ path: 'id_vattu', populate: { path: 'id_danh_diem' } })
        .populate({ path: 'id_tinhtrang' }).lean();
    }
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}

export async function create(req, res) {
  try {
    const { error, value } = Service.validate(req.body);
    if (error) return responseAction.error(res, error, 400);
    let data = await Model.create(value);
    if (data && req.body.chitiet && Array.isArray(req.body.chitiet)) {
      data = data.toObject();
      req.body.chitiet.forEach(row => {
        row.id_suachua = data._id;
      });
      data.chitiet = await SuaChuaChiTietService.create(req.body.chitiet);
    }
    return responseAction.success(res, data);
  } catch (err) {
    console.error(err);
    return responseAction.error(res, err, 500);
  }
}

export async function getAll(req, res) {
  try {
    const query = queryHelper.extractQueryParam(req);
    const { criteria, options } = query;
    options.populate = [
      { path: 'id_donvi', select: 'ten_don_vi' },
    ];
    const data = await Model.paginate(criteria, options);
    responseAction.success(res, data);
  } catch (err) {
    responseAction.error(res, err);
  }
}
