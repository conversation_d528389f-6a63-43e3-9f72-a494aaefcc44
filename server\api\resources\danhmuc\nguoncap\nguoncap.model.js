import mongoose, { Schema } from 'mongoose';
import { NGUONCAP } from '../../../constant/dbCollections';
import mongoosePaginate from 'mongoose-paginate-v2';

const schema = new Schema({
  ten_nguon_cap: { type: String, required: true, validate: /\S+/ },
  id_nguon_cap: { type: String, required: true, unique: true},
  isActive: { type: Boolean, default: true },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  versionKey: false,
});

schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(NGUONCAP, schema);
