import mongoose, { Schema } from 'mongoose';
import { TOCHUC_QUANLY } from '../../constant/dbCollections';
import mongoosePaginate from 'mongoose-paginate-v2';
import { CAP_DON_VI } from '../../constant/constant';

const schema = new Schema({
  ten_don_vi: { type: String, required: true, validate: /\S+/ },
  ma_don_vi: { type: String, required: true },
  ma_in: { type: String },
  duong_day_nong: { type: String, required: false },
  email: { type: String, required: false },
  fax: { type: String, required: false },
  dia_chi: { type: String, required: false },
  cap_don_vi: {
    type: String,
    enum: Object.values(CAP_DON_VI),
    required: true,
  },
  don_vi_cha_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: TOCHUC_QUANLY,
  },
  is_root: { type: Boolean, default: false, select: false },
  is_deleted: { type: Boolean, default: false, select: false },
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
export { schema as DocumentSchema };
export default mongoose.model(TOCHUC_QUANLY, schema);

